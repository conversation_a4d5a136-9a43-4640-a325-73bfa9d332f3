'use client';

import React, { Suspense, lazy } from 'react';
import { useParams } from 'next/navigation';
import { useStoreConfig } from '@/contexts/StoreConfigContext';
import DashboardSkeleton from '@/components/skeletons/DashboardSkeleton';

// Lazy load the admin analytics component for better performance
const AdminAnalyticsPage = lazy(() => import('@/app/admin/analytics/page'));

export default function StoreAdminAnalytics() {
  const params = useParams();
  const { storeConfig } = useStoreConfig();
  const storeHandle = params.storeHandle as string;

  return (
    <div className='space-y-6'>
      {/* Store-specific header */}
      <div className='bg-white shadow-sm border-b border-gray-200 rounded-lg'>
        <div className='px-6 py-4'>
          <div className='flex items-center justify-between'>
            <div>
              <h1 className='text-2xl font-bold text-gray-900'>Analytics</h1>
              <p className='text-sm text-gray-500'>
                View performance metrics for {storeConfig?.store_name}
              </p>
            </div>
            <div className='flex items-center space-x-3'>
              <div className='flex items-center space-x-2'>
                <span className='text-sm text-gray-500'>Period:</span>
                <select className='border border-gray-300 rounded-md px-3 py-1 text-sm'>
                  <option value='7d'>Last 7 days</option>
                  <option value='30d'>Last 30 days</option>
                  <option value='90d'>Last 90 days</option>
                  <option value='1y'>Last year</option>
                </select>
              </div>
              <button className='inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50'>
                <svg
                  className='w-4 h-4 mr-2'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
                  />
                </svg>
                Export Report
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Analytics content - Using actual admin component */}
      <Suspense fallback={<DashboardSkeleton />}>
        <AdminAnalyticsPage />
      </Suspense>
    </div>
  );
}
