'use client';

import React, { Suspense, lazy } from 'react';
import { useParams } from 'next/navigation';
import { useStoreConfig } from '@/contexts/StoreConfigContext';
import DashboardSkeleton from '@/components/skeletons/DashboardSkeleton';

// Lazy load the admin coupons component for better performance
const AdminCouponsPage = lazy(() => import('@/app/admin/coupons/page'));

export default function StoreAdminCoupons() {
  const params = useParams();
  const { storeConfig } = useStoreConfig();
  const storeHandle = params.storeHandle as string;

  return (
    <div className='space-y-6'>
      {/* Store-specific header */}
      {/* <div className='bg-white shadow-sm border-b border-gray-200 rounded-lg'>
        <div className='px-6 py-4'>
          <div className='flex items-center justify-between'>
            <div>
              <h1 className='text-2xl font-bold text-gray-900'>Coupons</h1>
              <p className='text-sm text-gray-500'>
                Manage discount coupons for {storeConfig?.store_name}
              </p>
            </div>
            <div className='flex items-center space-x-3'>
              <a
                href={`/${storeHandle}/admin/coupons/new`}
                className='inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700'
              >
                <svg
                  className='w-4 h-4 mr-2'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M12 6v6m0 0v6m0-6h6m-6 0H6'
                  />
                </svg>
                Add Coupon
              </a>
            </div>
          </div>
        </div>
      </div> */}

      {/* Coupons content - Using actual admin component */}
      <Suspense fallback={<DashboardSkeleton />}>
        <AdminCouponsPage />
      </Suspense>
    </div>
  );
}
