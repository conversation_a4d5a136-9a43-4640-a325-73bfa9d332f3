'use client';

import React, { useEffect } from 'react';
import { useParams } from 'next/navigation';
import { useStoreConfig } from '@/contexts/StoreConfigContext';
import AdminLayout from '@/components/layouts/AdminLayout';
import { useAuthStore } from '@/stores/authStore';
import { LoadingBackdropProvider } from '@/contexts/LoadingBackdropContext';

interface AdminLayoutProps {
  children: React.ReactNode;
}

export default function StoreAdminLayout({ children }: AdminLayoutProps) {
  const params = useParams();
  const { storeConfig } = useStoreConfig();
  const { user } = useAuthStore();

  const storeHandle = params.storeHandle as string;

  // Admin routes should NOT apply any store themes
  // Theme isolation: Admin interface maintains its own styling

  return (
    <LoadingBackdropProvider>
      <AdminLayout>{children}</AdminLayout>
    </LoadingBackdropProvider>
  );
}
