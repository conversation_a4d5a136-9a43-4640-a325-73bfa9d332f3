'use client';

import React, { Suspense, useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { useStoreConfig } from '@/contexts/StoreConfigContext';
import DashboardSkeleton from '@/components/skeletons/DashboardSkeleton';
import { useAuthStore } from '@/stores/authStore';
import { useLoadingBackdrop } from '@/contexts/LoadingBackdropContext';
import {
  ShoppingBagIcon,
  UsersIcon,
  ClipboardDocumentListIcon,
  CurrencyDollarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
} from '@heroicons/react/24/outline';
import AdminDashboard from '@/app/admin/page';

// Store-specific admin dashboard component
function StoreAdminDashboardContent() {
  const params = useParams();
  const { storeConfig } = useStoreConfig();
  const storeHandle = params.storeHandle as string;
  const { user, token } = useAuthStore();
  const { showLoading, hideLoading } = useLoadingBackdrop();

  const [stats, setStats] = useState({
    totalOrders: 0,
    totalProducts: 0,
    totalCustomers: 0,
    totalRevenue: 0,
  });

  useEffect(() => {
    hideLoading();
    // Load dashboard stats here
    // This is where you would fetch real data from your Medusa backend
    setStats({
      totalOrders: 156,
      totalProducts: 89,
      totalCustomers: 234,
      totalRevenue: 45670,
    });
  }, [hideLoading]);

  const statCards = [
    {
      name: 'Total Orders',
      value: stats.totalOrders.toString(),
      change: '+12%',
      changeType: 'increase' as const,
      icon: ClipboardDocumentListIcon,
    },
    {
      name: 'Products',
      value: stats.totalProducts.toString(),
      change: '+5%',
      changeType: 'increase' as const,
      icon: ShoppingBagIcon,
    },
    {
      name: 'Customers',
      value: stats.totalCustomers.toString(),
      change: '+8%',
      changeType: 'increase' as const,
      icon: UsersIcon,
    },
    {
      name: 'Revenue',
      value: `₹${stats.totalRevenue.toLocaleString()}`,
      change: '+15%',
      changeType: 'increase' as const,
      icon: CurrencyDollarIcon,
    },
  ];

  return (
    <div className='space-y-6'>
      {/* Page Header */}
      <div>
        <h1 className='text-2xl font-bold text-gray-900'>Dashboard-1</h1>
        <p className='text-gray-600 mt-1'>
          Welcome to your {storeConfig?.storeName || 'store'} admin dashboard
        </p>
      </div>

      {/* Stats Grid */}
      <div className='grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4'>
        {statCards.map(stat => (
          <div
            key={stat.name}
            className='bg-white overflow-hidden shadow rounded-lg'
          >
            <div className='p-5'>
              <div className='flex items-center'>
                <div className='flex-shrink-0'>
                  <stat.icon
                    className='h-6 w-6 text-gray-400'
                    aria-hidden='true'
                  />
                </div>
                <div className='ml-5 w-0 flex-1'>
                  <dl>
                    <dt className='text-sm font-medium text-gray-500 truncate'>
                      {stat.name}
                    </dt>
                    <dd className='flex items-baseline'>
                      <div className='text-2xl font-semibold text-gray-900'>
                        {stat.value}
                      </div>
                      <div
                        className={`ml-2 flex items-baseline text-sm font-semibold ${
                          stat.changeType === 'increase'
                            ? 'text-green-600'
                            : 'text-red-600'
                        }`}
                      >
                        {stat.changeType === 'increase' ? (
                          <ArrowTrendingUpIcon
                            className='self-center flex-shrink-0 h-4 w-4 text-green-500'
                            aria-hidden='true'
                          />
                        ) : (
                          <ArrowTrendingDownIcon
                            className='self-center flex-shrink-0 h-4 w-4 text-red-500'
                            aria-hidden='true'
                          />
                        )}
                        <span className='sr-only'>
                          {stat.changeType === 'increase'
                            ? 'Increased'
                            : 'Decreased'}{' '}
                          by
                        </span>
                        {stat.change}
                      </div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className='bg-white shadow rounded-lg'>
        <div className='px-4 py-5 sm:p-6'>
          <h3 className='text-lg leading-6 font-medium text-gray-900'>
            Quick Actions
          </h3>
          <div className='mt-5 grid grid-cols-1 gap-3 sm:grid-cols-3'>
            <a
              href={`/${storeHandle}/admin/products`}
              className='inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
            >
              Add Product
            </a>
            <a
              href={`/${storeHandle}/admin/orders`}
              className='inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
            >
              View Orders
            </a>
            <a
              href={`/${storeHandle}/admin/customers`}
              className='inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
            >
              Manage Customers
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function StoreAdminDashboard() {
  return (
    <AdminDashboard />
    // <Suspense fallback={<DashboardSkeleton />}>
    // </Suspense>

    // <Suspense fallback={<DashboardSkeleton />}>
    //   <StoreAdminDashboardContent />
    // </Suspense>
  );
}
