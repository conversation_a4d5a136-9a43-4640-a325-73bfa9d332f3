'use client';

import React, { Suspense, useState, useEffect } from 'react';
import { useStoreConfig } from '@/contexts/StoreConfigContext';
import DashboardSkeleton from '@/components/skeletons/DashboardSkeleton';
import { useLoadingBackdrop } from '@/contexts/LoadingBackdropContext';
import { useAuthStore } from '@/stores/authStore';
import { BuildingStorefrontIcon, BellIcon } from '@heroicons/react/24/outline';

// Store-specific settings component
function StoreSettingsContent() {
  const { storeConfig } = useStoreConfig();
  const { user } = useAuthStore();
  const { hideLoading } = useLoadingBackdrop();

  const [settings, setSettings] = useState({
    storeName: storeConfig?.store_name || '',
    storeDescription: '',
    contactEmail: user?.email || '',
    contactPhone: '',
    address: '',
    notifications: {
      orderUpdates: true,
      lowStock: true,
      customerMessages: true,
    },
  });

  useEffect(() => {
    hideLoading();
    // Load settings data here
    // This is where you would fetch real data from your backend
  }, [hideLoading]);

  const handleSave = () => {
    // Save settings logic here
    console.log('Saving settings:', settings);
  };

  return (
    <div className='space-y-6'>
      {/* Page Header */}
      <div>
        <h1 className='text-2xl font-bold text-gray-900'>Settings</h1>
        <p className='text-gray-600 mt-1'>Configure settings for your store</p>
      </div>

      {/* Settings Sections */}
      <div className='space-y-6'>
        {/* Store Information */}
        <div className='bg-white shadow rounded-lg'>
          <div className='px-4 py-5 sm:p-6'>
            <div className='flex items-center mb-4'>
              <BuildingStorefrontIcon className='h-5 w-5 text-gray-400 mr-2' />
              <h3 className='text-lg font-medium text-gray-900'>
                Store Information
              </h3>
            </div>
            <div className='grid grid-cols-1 gap-6 sm:grid-cols-2'>
              <div>
                <label className='block text-sm font-medium text-gray-700'>
                  Store Name
                </label>
                <input
                  type='text'
                  value={settings.storeName}
                  onChange={e =>
                    setSettings({ ...settings, storeName: e.target.value })
                  }
                  className='mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm'
                />
              </div>
              <div>
                <label className='block text-sm font-medium text-gray-700'>
                  Contact Email
                </label>
                <input
                  type='email'
                  value={settings.contactEmail}
                  onChange={e =>
                    setSettings({ ...settings, contactEmail: e.target.value })
                  }
                  className='mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm'
                />
              </div>
              <div className='sm:col-span-2'>
                <label className='block text-sm font-medium text-gray-700'>
                  Store Description
                </label>
                <textarea
                  rows={3}
                  value={settings.storeDescription}
                  onChange={e =>
                    setSettings({
                      ...settings,
                      storeDescription: e.target.value,
                    })
                  }
                  className='mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm'
                />
              </div>
            </div>
          </div>
        </div>

        {/* Notification Settings */}
        <div className='bg-white shadow rounded-lg'>
          <div className='px-4 py-5 sm:p-6'>
            <div className='flex items-center mb-4'>
              <BellIcon className='h-5 w-5 text-gray-400 mr-2' />
              <h3 className='text-lg font-medium text-gray-900'>
                Notifications
              </h3>
            </div>
            <div className='space-y-4'>
              <div className='flex items-center justify-between'>
                <div>
                  <h4 className='text-sm font-medium text-gray-900'>
                    Order Updates
                  </h4>
                  <p className='text-sm text-gray-500'>
                    Get notified when orders are placed or updated
                  </p>
                </div>
                <input
                  type='checkbox'
                  checked={settings.notifications.orderUpdates}
                  onChange={e =>
                    setSettings({
                      ...settings,
                      notifications: {
                        ...settings.notifications,
                        orderUpdates: e.target.checked,
                      },
                    })
                  }
                  className='h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded'
                />
              </div>
              <div className='flex items-center justify-between'>
                <div>
                  <h4 className='text-sm font-medium text-gray-900'>
                    Low Stock Alerts
                  </h4>
                  <p className='text-sm text-gray-500'>
                    Get notified when products are running low
                  </p>
                </div>
                <input
                  type='checkbox'
                  checked={settings.notifications.lowStock}
                  onChange={e =>
                    setSettings({
                      ...settings,
                      notifications: {
                        ...settings.notifications,
                        lowStock: e.target.checked,
                      },
                    })
                  }
                  className='h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded'
                />
              </div>
            </div>
          </div>
        </div>

        {/* Save Button */}
        <div className='flex justify-end'>
          <button
            onClick={handleSave}
            className='inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
          >
            Save Changes
          </button>
        </div>
      </div>
    </div>
  );
}

export default function StoreAdminSettings() {
  return (
    <Suspense fallback={<DashboardSkeleton />}>
      <StoreSettingsContent />
    </Suspense>
  );
}
