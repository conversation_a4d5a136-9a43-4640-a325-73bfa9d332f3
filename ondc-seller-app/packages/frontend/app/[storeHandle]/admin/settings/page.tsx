'use client';

import React, { Suspense, lazy } from 'react';
import { useParams } from 'next/navigation';
import { useStoreConfig } from '@/contexts/StoreConfigContext';
import DashboardSkeleton from '@/components/skeletons/DashboardSkeleton';

// Lazy load the admin settings component for better performance
const AdminSettingsPage = lazy(() => import('@/app/admin/settings/page'));

export default function StoreAdminSettings() {
  const params = useParams();
  const { storeConfig } = useStoreConfig();
  const storeHandle = params.storeHandle as string;

  return (
    <div className='space-y-6'>
      {/* Store-specific header */}
      <div className='bg-white shadow-sm border-b border-gray-200 rounded-lg'>
        <div className='px-6 py-4'>
          <div className='flex items-center justify-between'>
            <div>
              <h1 className='text-2xl font-bold text-gray-900'>Settings</h1>
              <p className='text-sm text-gray-500'>
                Configure settings for {storeConfig?.store_name}
              </p>
            </div>
            <div className='flex items-center space-x-3'>
              <button className='inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700'>
                <svg
                  className='w-4 h-4 mr-2'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4'
                  />
                </svg>
                Save Changes
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Settings content - Using actual admin component */}
      <Suspense fallback={<DashboardSkeleton />}>
        <AdminSettingsPage />
      </Suspense>
    </div>
  );
}
