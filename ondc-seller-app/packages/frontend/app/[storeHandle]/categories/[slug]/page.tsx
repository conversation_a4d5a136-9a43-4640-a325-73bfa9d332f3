'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import ProductListingPage, {
  Product,
  ProductListingConfig,
} from '@/components/common/ProductListingPage';
import { useMedusaBackendProducts } from '@/hooks/useMedusaBackendProducts';
import { useCategoryStore } from '@/stores/categoriesStore';
import { MedusaProduct } from '@/lib/medusa-backend-api';
import { useStoreConfigStore } from '@/stores/storeConfigStore';
// TODO: Replace with real API calls
// import { getProductsByCategory } from '@/lib/medusa-backend-api';

export default function CategoryDetailPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const categorySlug = params.slug as string;
  const subcategory = searchParams.get('subcategory');
  const { storeHandle } = useStoreConfigStore();
  // const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeSubcategory, setActiveSubcategory] = useState<string>('');

  const {
    products,
    loading,

    getProductsByCategory,
  } = useMedusaBackendProducts();
  const selectedCategoryId = useCategoryStore(
    state => state.selectedCategoryId
  );
  const getCategoryById = useCategoryStore(state => state.getCategoryById);

  const categoryData = useMemo(() => {
    if (selectedCategoryId == '') return;
    return getCategoryById(selectedCategoryId);
  }, [selectedCategoryId]);

  useEffect(() => {
    if (categorySlug) {
      setActiveSubcategory(subcategory || '');
    }
    if (selectedCategoryId !== '') getProductsByCategory(selectedCategoryId);
  }, [categorySlug, subcategory, selectedCategoryId]);

  const handleSubcategoryChange = (subcategoryId: string) => {
    if (subcategoryId === 'all') {
      setActiveSubcategory('');
      if (categorySlug) {
        getProductsByCategory(selectedCategoryId);
      }
    } else {
      setActiveSubcategory(subcategoryId);
      if (categorySlug) {
        getProductsByCategory(subcategoryId);
      }
    }
  };

  if (!categorySlug) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <div className='text-center'>
          <h1 className='text-2xl font-bold text-gray-900 mb-4'>
            Category Not Found
          </h1>
          <p className='text-gray-600'>Please select a valid category.</p>
        </div>
      </div>
    );
  }

  const categoryName = categorySlug
    .replace(/-/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase());

  const allSubcategoryFilters = useMemo(() => {
    if (categoryData?.subcategories.length < 1) return [];
    return categoryData?.subcategories?.map(data => ({
      id: data.id,
      name: data.name,
    }));
  }, [categoryData?.subcategories]);

  const productData = useMemo(() => {
    if (products.length < 1) return [];
    const convertedProducts: Product[] = products.map(
      (apiProduct: MedusaProduct, index: number) => {
        // Safely access variant data
        const firstVariant = apiProduct.variants?.[0];
        const prices = firstVariant?.metadata?.additional_data?.product_prices;
        const firstPrice =
          Array.isArray(prices) && prices.length > 0 ? prices[0] : {};

        return {
          id: apiProduct.id, // Use original product ID instead of converting to number
          name: apiProduct.title,
          slug:
            apiProduct.handle ||
            apiProduct.title.toLowerCase().replace(/\s+/g, '-'),
          originalPrice: firstPrice.original_price || 2299, // Default price since API doesn't provide pricing
          salePrice: firstPrice.sale_price || 1399, // Default sale price
          // discount: 33, // Default discount
          image:
            apiProduct.thumbnail ||
            `https://picsum.photos/400/400?random=${apiProduct.id}`,
          category: 'Products',
          subcategory: 'General',
          tenantId: 'default',
          rating: 4.5, // Default rating
          reviewCount: Math.floor(Math.random() * 500) + 50, // Random review count
          badge: apiProduct.status === 'published' ? 'Available' : 'Draft',
          brand: 'API Brand',
          inStock: apiProduct.status === 'published',
          tags: apiProduct.tags?.map(tag => tag.value) || [],
        };
      }
    );
    return convertedProducts;
  }, [products]);

  // Get subcategory display name
  const subcategoryDisplayName = activeSubcategory
    ? allSubcategoryFilters.find(sub => sub.id === activeSubcategory)?.name
    : null;

  // Format category name for display
  const categoryDisplayName =
    categoryName.charAt(0).toUpperCase() + categoryName.slice(1);

  const config: ProductListingConfig = {
    title:
      activeSubcategory && activeSubcategory !== 'all'
        ? subcategoryDisplayName || activeSubcategory
        : categoryDisplayName,
    subtitle:
      activeSubcategory && activeSubcategory !== 'all'
        ? `Discover our collection of ${
            subcategoryDisplayName?.toLowerCase() || 'products'
          }`
        : `Discover amazing ${categoryName.toLowerCase()} products`,
    showDiscountBadge: true,
    showSaleBadge: true,
    showPriceComparison: true,
    showSavingsAmount: true,
    buttonText: 'Add to Cart',
    buttonStyle: 'primary',
    storeHandle: storeHandle,
    breadcrumbs:
      activeSubcategory && activeSubcategory !== 'all'
        ? [
            { label: 'Home', href: `/${storeHandle}` },
            {
              label: categoryDisplayName,
              href: `/${storeHandle}/categories/${categorySlug}`,
            },
            { label: subcategoryDisplayName || activeSubcategory },
          ]
        : [
            { label: 'Home', href: `/${storeHandle}` },
            { label: categoryDisplayName },
          ],
    subcategoryFilters: allSubcategoryFilters, // Always show subcategory filters on main category page
    activeSubcategory: activeSubcategory,
    showSubcategoryFilter: true, // Always show subcategory filters on category page
  };

  console.log({ productData });
  return (
    <ProductListingPage
      products={productData}
      config={config}
      isLoading={loading}
      onSubcategoryChange={handleSubcategoryChange}
    />
  );
}
