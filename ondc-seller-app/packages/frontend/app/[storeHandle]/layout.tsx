'use client';

import React, { useEffect } from 'react';
import { useParams, useRouter, usePathname } from 'next/navigation';
import { useStoreConfig } from '@/contexts/StoreConfigContext';
import { StoreThemeProvider } from '@/contexts/StoreThemeContext';
import { StoreMuiThemeProvider } from '@/contexts/StoreMuiThemeProvider';
import ThemedStoreHeader from '@/components/layouts/ThemedStoreHeader';
import ThemedStoreFooter from '@/components/layouts/ThemedStoreFooter';
import { useAuthStore } from '@/stores/authStore';

interface StoreLayoutProps {
  children: React.ReactNode;
}

export default function StoreLayout({ children }: StoreLayoutProps) {
  const params = useParams();
  const router = useRouter();
  const pathname = usePathname();
  const { user, token } = useAuthStore();
  const { storeConfig } = useStoreConfig();

  const storeHandle = params.storeHandle as string;
  const isAdminRoute = pathname.includes('/admin');

  useEffect(() => {
    if (isAdminRoute) {
      // Only redirect if we're sure there's no token and not in development mode
      const isDevelopment = process.env.NODE_ENV === 'development';

      // Give time for token to load from storage
      // const timeoutId = setTimeout(() => {
      if (!token && !isDevelopment) {
        console.log(
          '🔒 [StoreLayout] Admin route requires authentication, redirecting to login'
        );
        router.push('/');
      }
      // }, 1000); // Wait 1 second for token to load

      // return () => clearTimeout(timeoutId);
    }
  }, [user, token, isAdminRoute, router]);

  // Show error if validation failed
  // TEMPORARY: Comment out for testing
  if (false) {
    // Disabled error handling for performance
    return (
      <div className='min-h-screen flex items-center justify-center bg-gray-50'>
        <div className='max-w-md w-full bg-white shadow-lg rounded-lg p-6'>
          <div className='text-center'>
            <div className='mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100'>
              <svg
                className='h-6 w-6 text-red-600'
                fill='none'
                viewBox='0 0 24 24'
                stroke='currentColor'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z'
                />
              </svg>
            </div>
            <h3 className='mt-2 text-sm font-medium text-gray-900'>
              Access Denied
            </h3>
            <p className='mt-1 text-sm text-gray-500'>Access denied</p>
            <div className='mt-6'>
              <button
                type='button'
                className='inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                onClick={() => router.push('/auth/login')}
              >
                Go to Login
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Render children if validation passed
  if (isAdminRoute) {
    // Admin routes don't need header/footer or theming
    // Ensure no theme variables are applied to admin routes
    return <>{children}</>;
  } else {
    // Store routes get header, footer, and theming
    return (
      <StoreThemeProvider>
        <StoreMuiThemeProvider>
          <div className='min-h-screen flex flex-col'>
            <ThemedStoreHeader
              storeConfig={storeConfig}
              storeHandle={storeHandle}
            />
            <main className='flex-1'>{children}</main>
            <ThemedStoreFooter storeConfig={storeConfig} />
          </div>
        </StoreMuiThemeProvider>
      </StoreThemeProvider>
    );
  }
}
