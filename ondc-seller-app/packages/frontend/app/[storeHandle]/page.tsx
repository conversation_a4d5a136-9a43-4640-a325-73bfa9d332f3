'use client';

import React, { useEffect } from 'react';
import { useParams } from 'next/navigation';
import { useStoreConfig } from '@/contexts/StoreConfigContext';
import LoadingBackdrop from '@/components/LoadingBackdrop';
import StoreHomepage from '@/components/store/StoreHomepage';
import { getStoreConfiguration } from '@/lib/api/store-config';
import { useStoreConfigStore } from '@/stores/storeConfigStore';
import { useStoreColorPalette } from '@/hooks/useStoreColorPalette';
import { useQuery } from '@tanstack/react-query';

const StorePage = () => {
  const params = useParams();
  const { storeConfig, loading } = useStoreConfig();
  const {
    setStoreName,
    setStoreHandle,
    setStoreLogo,
    setStoreTheme,
    setStoreAddress,
    setStoreEmail,
    setStoreConatctNumber,
    setStoreDescription,
    setStoreColorPalette,
  } = useStoreConfigStore();

  const { enableAutoApply } = useStoreColorPalette();

  // const {  user } = useStoreConfig();
  const storeHandle = params?.storeHandle as string;
  const fetchStoreDetails = async () => {
    try {
      const response = await getStoreConfiguration(storeHandle);
      console.log('fetchStoreDetails:::::::::', response);
      if (response.data.length > 0) {
        setStoreName(response.data[0].store_name);
        setStoreHandle(response.data[0].store_handle);
        setStoreLogo(response.data[0].store_logo_url || '');
        setStoreTheme(response.data[0].store_theme);

        setStoreColorPalette({
          muted: response.data[0].store_color_palette.muted,
          vibrant: response.data[0].store_color_palette.vibrant,
          dominant: response.data[0].store_color_palette.dominant,
          mutedDark: response.data[0].store_color_palette.mutedDark,
          mutedLight: response.data[0].store_color_palette.mutedLight,
          vibrantDark: response.data[0].store_color_palette.vibrantDark,
          vibrantLight: response.data[0].store_color_palette.vibrantLight,
        });
        setStoreAddress(
          `${response.data[0].address_line_1}, 
          ${response.data[0].address_line_2}, 
         ${response.data[0].state}, ${response.data[0].country}`
        );
        setStoreEmail(response.data[0].store_email);
        setStoreConatctNumber(response.data[0].phone);
        setStoreDescription(response.data[0].store_description);

        // Auto-apply color palette after store configuration is loaded
        setTimeout(() => {
          enableAutoApply(true);
        }, 500);
      }
    } catch (error) {
      console.error('Error fetching store details from strapi:', error);
    }
  };
  const {
    isLoading,
    error,
    data: storeData,
  } = useQuery({
    queryKey: ['repoData', storeHandle],
    queryFn: () => getStoreConfiguration(storeHandle),
    // suspense: true,
    enabled: !!storeHandle,
  });
  useEffect(() => {
    if (storeData?.data.length > 0) {
      setStoreName(storeData?.data[0]?.store_name);
      setStoreHandle(storeData?.data[0]?.store_handle);
      setStoreLogo(storeData?.data[0]?.store_logo_url || '');
      setStoreTheme(storeData?.data[0]?.store_theme);

      setStoreColorPalette({
        muted: storeData?.data[0]?.store_color_palette?.muted,
        vibrant: storeData?.data[0]?.store_color_palette?.vibrant,
        dominant: storeData?.data[0]?.store_color_palette?.dominant,
        mutedDark: storeData?.data[0]?.store_color_palette?.mutedDark,
        mutedLight: storeData?.data[0]?.store_color_palette?.mutedLight,
        vibrantDark: storeData?.data[0]?.store_color_palette?.vibrantDark,
        vibrantLight: storeData?.data[0]?.store_color_palette?.vibrantLight,
      });
      setStoreAddress(
        `${storeData?.data[0]?.address_line_1},
        ${storeData?.data[0]?.address_line_2},
       ${storeData?.data[0]?.state}, ${storeData?.data[0]?.country}`
      );
      setStoreEmail(storeData?.data[0]?.store_email);
      setStoreConatctNumber(storeData?.data[0]?.phone);
      setStoreDescription(storeData?.data[0]?.store_description);

      // Auto-apply color palette after store configuration is loaded
    }
  }, [storeData]);
  // useEffect(() => {
  //   if (storeHandle) fetchStoreDetails();
  // }, [storeHandle]);
  if (isLoading) {
    return <LoadingBackdrop open={true} />;
  }

  return <StoreHomepage storeHandle={storeHandle} storeConfig={storeConfig} />;
};
export default StorePage;
