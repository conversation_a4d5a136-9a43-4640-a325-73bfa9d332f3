'use client';

import React from 'react';
import { useParams } from 'next/navigation';
import { useStoreConfig } from '@/contexts/StoreConfigContext';
import LoadingBackdrop from '@/components/LoadingBackdrop';

// Import existing products components
import ProductGrid from '@/components/ProductGrid';
import CategoryFilters from '@/components/CategoryFilters';

export default function StoreProductsPage() {
  const params = useParams();
  const { storeConfig, loading } = useStoreConfig();
  const storeHandle = params.storeHandle as string;

  if (loading) {
    return <LoadingBackdrop open={true} />;
  }

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-3xl font-extrabold text-gray-900">
              {storeConfig?.store_name} Products
            </h1>
            <p className="mt-4 text-lg text-gray-600">
              Discover our amazing collection of products
            </p>
          </div>
        </div>
      </div>

      {/* Products Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Filters Sidebar */}
          <div className="lg:w-1/4">
            <CategoryFilters storeHandle={storeHandle} />
          </div>

          {/* Products Grid */}
          <div className="lg:w-3/4">
            <ProductGrid storeHandle={storeHandle} />
          </div>
        </div>
      </div>
    </div>
  );
}
