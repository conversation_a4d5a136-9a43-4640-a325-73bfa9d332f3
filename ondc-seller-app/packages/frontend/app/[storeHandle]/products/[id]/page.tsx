// // 'use client';

// // import React, { useEffect, useState } from 'react';
// // import { useParams } from 'next/navigation';
// // import { useStoreConfig } from '@/contexts/StoreConfigContext';
// // import LoadingBackdrop from '@/components/LoadingBackdrop';

// // export default function StoreProductDetailsPage() {
// //   const params = useParams();
// //   const { storeConfig, loading } = useStoreConfig();
// //   const storeHandle = params.storeHandle as string;
// //   const productId = params.id as string;
// //   const [product, setProduct] = useState<any>(null);
// //   const [productLoading, setProductLoading] = useState(true);

// //   useEffect(() => {
// //     const fetchProduct = async () => {
// //       try {
// //         setProductLoading(true);
// //         // Fetch product details from your API
// //         const response = await fetch(`/api/products/${productId}`);
// //         if (response.ok) {
// //           const productData = await response.json();
// //           setProduct(productData);
// //         }
// //       } catch (error) {
// //         console.error('Error fetching product:', error);
// //       } finally {
// //         setProductLoading(false);
// //       }
// //     };

// //     if (productId) {
// //       fetchProduct();
// //     }
// //   }, [productId]);

// //   if (loading || productLoading) {
// //     return <LoadingBackdrop open={true} />;
// //   }

// //   if (!product) {
// //     return (
// //       <div className="min-h-screen flex items-center justify-center">
// //         <div className="text-center">
// //           <h1 className="text-2xl font-bold text-gray-900">Product Not Found</h1>
// //           <p className="mt-2 text-gray-600">The requested product could not be found.</p>
// //           <div className="mt-4">
// //             <a
// //               href={`/${storeHandle}/products`}
// //               className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
// //             >
// //               Back to Products
// //             </a>
// //           </div>
// //         </div>
// //       </div>
// //     );
// //   }

// //   return (
// //     <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
// //       <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
// //         {/* Product Images */}
// //         <div className="space-y-4">
// //           <div className="aspect-w-1 aspect-h-1 bg-gray-200 rounded-lg overflow-hidden">
// //             <img
// //               src={product.thumbnail || product.image || '/placeholder-product.jpg'}
// //               alt={product.title}
// //               className="w-full h-full object-center object-cover"
// //             />
// //           </div>
// //           {/* Additional product images can be added here */}
// //         </div>

// //         {/* Product Details */}
// //         <div className="space-y-6">
// //           <div>
// //             <h1 className="text-3xl font-extrabold text-gray-900">{product.title}</h1>
// //             <p className="mt-2 text-lg text-gray-600">{product.subtitle}</p>
// //           </div>

// //           {/* Price */}
// //           <div className="flex items-center space-x-4">
// //             <span className="text-3xl font-bold text-gray-900">
// //               ${product.variants?.[0]?.prices?.[0]?.amount || 'N/A'}
// //             </span>
// //             {product.variants?.[0]?.prices?.[0]?.compare_at_amount && (
// //               <span className="text-xl text-gray-500 line-through">
// //                 ${product.variants[0].prices[0].compare_at_amount}
// //               </span>
// //             )}
// //           </div>

// //           {/* Description */}
// //           <div>
// //             <h3 className="text-lg font-medium text-gray-900">Description</h3>
// //             <p className="mt-2 text-gray-600">{product.description}</p>
// //           </div>

// //           {/* Add to Cart Button */}
// //           <div className="flex space-x-4">
// //             <button
// //               type="button"
// //               className="flex-1 bg-blue-600 border border-transparent rounded-md py-3 px-8 flex items-center justify-center text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
// //             >
// //               Add to Cart
// //             </button>
// //             <button
// //               type="button"
// //               className="flex-1 bg-gray-50 border border-gray-300 rounded-md py-3 px-8 flex items-center justify-center text-base font-medium text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
// //             >
// //               Add to Wishlist
// //             </button>
// //           </div>

// //           {/* Product Metadata */}
// //           {product.metadata && Object.keys(product.metadata).length > 0 && (
// //             <div>
// //               <h3 className="text-lg font-medium text-gray-900">Product Details</h3>
// //               <dl className="mt-2 space-y-2">
// //                 {Object.entries(product.metadata).map(([key, value]) => (
// //                   <div key={key} className="flex justify-between">
// //                     <dt className="text-sm font-medium text-gray-500 capitalize">
// //                       {key.replace(/_/g, ' ')}:
// //                     </dt>
// //                     <dd className="text-sm text-gray-900">{value as string}</dd>
// //                   </div>
// //                 ))}
// //               </dl>
// //             </div>
// //           )}

// //           {/* Store Information */}
// //           <div className="border-t pt-6">
// //             <h3 className="text-lg font-medium text-gray-900">Sold by</h3>
// //             <div className="mt-2 flex items-center space-x-3">
// //               {storeConfig?.store_logo && (
// //                 <img
// //                   src={storeConfig.store_logo}
// //                   alt={storeConfig.store_name}
// //                   className="h-10 w-10 rounded-full object-cover"
// //                 />
// //               )}
// //               <div>
// //                 <p className="text-sm font-medium text-gray-900">{storeConfig?.store_name}</p>
// //                 <p className="text-sm text-gray-500">{storeConfig?.city}, {storeConfig?.state}</p>
// //               </div>
// //             </div>
// //           </div>
// //         </div>
// //       </div>
// //     </div>
// //   );
// // }

// 'use client';

// import * as React from 'react';
// import { useCategoryStore } from '@/stores/categoriesStore';
// import MedusaProductDetailPage from '@/components/products/MedusaProductDetailPage';
// import { MedusaCartProvider } from '@/hooks/useMedusaCart';
// import { ToastProvider } from '@/components/common/ToastProvider';

// const ProductPage = ({ params }: { params: Promise<{ id: string }> }) => {
//   const { id: productId } = React.use(params);
//   const selectedSubCategoryId = useCategoryStore(
//     state => state.selectedSubCategoryId
//   );
//   const selectedCategoryId = useCategoryStore(
//     state => state.selectedCategoryId
//   );

//   return (
//     <MedusaCartProvider>
//       <ToastProvider>
//         <MedusaProductDetailPage
//           productId={productId}
//           categoryId={selectedCategoryId}
//           subCategoryId={selectedSubCategoryId}
//         />
//       </ToastProvider>
//     </MedusaCartProvider>
//   );
// };

// export default ProductPage;

'use client';

import * as React from 'react';
import { useCategoryStore } from '@/stores/categoriesStore';
import MedusaProductDetailPage from '@/components/products/MedusaProductDetailPage';
import { MedusaCartProvider } from '@/hooks/useMedusaCart';
import { ToastProvider } from '@/components/common/ToastProvider';
import { useStoreConfigStore } from '@/stores/storeConfigStore';

const ProductPage = ({ params }: { params: Promise<{ id: string }> }) => {
  const { id: productId } = React.use(params);

  const { storeHandle } = useStoreConfigStore();
  const selectedSubCategoryId = useCategoryStore(
    state => state.selectedSubCategoryId
  );
  const selectedCategoryId = useCategoryStore(
    state => state.selectedCategoryId
  );

  return (
    <MedusaCartProvider>
      <ToastProvider>
        <MedusaProductDetailPage
          productId={productId}
          categoryId={selectedCategoryId}
          subCategoryId={selectedSubCategoryId}
          storeHandle={storeHandle}
        />
      </ToastProvider>
    </MedusaCartProvider>
  );
};

export default ProductPage;
