'use client';

import React, { Suspense, useState, useEffect } from 'react';
import { AdminDashboardWrapper } from '@/components/admin/AdminSuspenseWrapper';
import { useOptimizedLoading } from '@/contexts/OptimizedLoadingContext';
import { useAdminTenant, useAdminTenantListener } from '@/hooks/useAdminTenant';
import DashboardSkeleton from '@/components/skeletons/DashboardSkeleton';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import LoadingBackdrop from '@/components/LoadingBackdrop';

import Image from 'next/image';
import {
  ShoppingBagIcon,
  UsersIcon,
  ClipboardDocumentListIcon,
  CurrencyDollarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  EyeIcon,
  PlusIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import ChartCard, {
  CustomerSplit<PERSON>hart,
  EnhancedDonut<PERSON>hart,
  RefundRate<PERSON>hart,
  VisitorTrendsChart,
} from '../components/ChartCard';
import {
  SalesStatisticsChart,
  LifetimeSalesChart,
} from '../components/InteractiveCharts';
import MUIKPIDashboard from '@/components/dashboard/MUIKPIDashboard';
import { useKPIData } from '@/hooks/useKPIData';
import AdvancedAnalyticsCharts from '@/components/analytics/AdvancedAnalyticsCharts';
import { useAnalytics } from '@/hooks/useAnalytics';
import QuickActions from '@/components/dashboard/QuickActions';
import ActivityFeed from '@/components/dashboard/ActivityFeed';
import NotificationSystem from '@/components/dashboard/NotificationSystem';
import medusaAdminAPI from '@/lib/medusa-admin-api';
import { formatCurrency } from '@/utils/formatCurrency';
import { useLoadingBackdrop } from '@/contexts/LoadingBackdropContext';
interface StatCard {
  name: string;
  value: string;
  change: string;
  changeType: 'increase' | 'decrease';
  icon: React.ComponentType<any>;
}

const recentOrders = [
  {
    id: '#ORD-001',
    customer: 'John Doe',
    email: '<EMAIL>',
    total: '₹1,299',
    status: 'Completed',
    date: '2024-01-15',
  },
  {
    id: '#ORD-002',
    customer: 'Jane Smith',
    email: '<EMAIL>',
    total: '₹2,499',
    status: 'Processing',
    date: '2024-01-15',
  },
  {
    id: '#ORD-003',
    customer: 'Mike Johnson',
    email: '<EMAIL>',
    total: '₹899',
    status: 'Shipped',
    date: '2024-01-14',
  },
  {
    id: '#ORD-004',
    customer: 'Sarah Wilson',
    email: '<EMAIL>',
    total: '₹3,299',
    status: 'Pending',
    date: '2024-01-14',
  },
  {
    id: '#ORD-005',
    customer: 'David Brown',
    email: '<EMAIL>',
    total: '₹1,799',
    status: 'Completed',
    date: '2024-01-13',
  },
];

// Mock data is now handled within the InteractiveCharts components

// Visitor data for different time periods
const visitorData = {
  '7days': [
    { label: 'Mon', mobile: 1250, desktop: 2100 },
    { label: 'Tue', mobile: 1400, desktop: 2300 },
    { label: 'Wed', mobile: 1600, desktop: 2500 },
    { label: 'Thu', mobile: 1350, desktop: 2200 },
    { label: 'Fri', mobile: 1800, desktop: 2800 },
    { label: 'Sat', mobile: 2200, desktop: 3200 },
    { label: 'Sun', mobile: 1900, desktop: 2900 },
  ],
  '30days': [
    { label: 'Week 1', mobile: 8500, desktop: 15200 },
    { label: 'Week 2', mobile: 9200, desktop: 16800 },
    { label: 'Week 3', mobile: 8800, desktop: 15900 },
    { label: 'Week 4', mobile: 10100, desktop: 18500 },
  ],
  '3months': [
    { label: 'Month 1', mobile: 35000, desktop: 62000 },
    { label: 'Month 2', mobile: 38000, desktop: 68000 },
    { label: 'Month 3', mobile: 42000, desktop: 75000 },
  ],
  '6months': [
    { label: 'Jan', mobile: 32000, desktop: 58000 },
    { label: 'Feb', mobile: 35000, desktop: 62000 },
    { label: 'Mar', mobile: 38000, desktop: 68000 },
    { label: 'Apr', mobile: 42000, desktop: 75000 },
    { label: 'May', mobile: 45000, desktop: 82000 },
    { label: 'Jun', mobile: 48000, desktop: 88000 },
  ],
  yearly: [
    { label: '2020', mobile: 420000, desktop: 780000 },
    { label: '2021', mobile: 480000, desktop: 890000 },
    { label: '2022', mobile: 520000, desktop: 950000 },
    { label: '2023', mobile: 580000, desktop: 1020000 },
  ],
};

// Browser data for different time periods
const browserData = {
  today: [
    { label: 'Chrome', value: 2450, color: '#4285F4' },
    { label: 'Safari', value: 1200, color: '#FF9500' },
    { label: 'Firefox', value: 800, color: '#FF7139' },
    { label: 'Edge', value: 650, color: '#0078D4' },
    { label: 'Others', value: 400, color: '#6B7280' },
  ],
  '7days': [
    { label: 'Chrome', value: 18500, color: '#4285F4' },
    { label: 'Safari', value: 9200, color: '#FF9500' },
    { label: 'Firefox', value: 6800, color: '#FF7139' },
    { label: 'Edge', value: 4200, color: '#0078D4' },
    { label: 'Others', value: 2800, color: '#6B7280' },
  ],
  '30days': [
    { label: 'Chrome', value: 78500, color: '#4285F4' },
    { label: 'Safari', value: 42000, color: '#FF9500' },
    { label: 'Firefox', value: 28500, color: '#FF7139' },
    { label: 'Edge', value: 18200, color: '#0078D4' },
    { label: 'Others', value: 12800, color: '#6B7280' },
  ],
  '3months': [
    { label: 'Chrome', value: 245000, color: '#4285F4' },
    { label: 'Safari', value: 128000, color: '#FF9500' },
    { label: 'Firefox', value: 89000, color: '#FF7139' },
    { label: 'Edge', value: 56000, color: '#0078D4' },
    { label: 'Others', value: 38000, color: '#6B7280' },
  ],
  '6months': [
    { label: 'Chrome', value: 520000, color: '#4285F4' },
    { label: 'Safari', value: 280000, color: '#FF9500' },
    { label: 'Firefox', value: 195000, color: '#FF7139' },
    { label: 'Edge', value: 125000, color: '#0078D4' },
    { label: 'Others', value: 85000, color: '#6B7280' },
  ],
  yearly: [
    { label: 'Chrome', value: 1250000, color: '#4285F4' },
    { label: 'Safari', value: 680000, color: '#FF9500' },
    { label: 'Firefox', value: 480000, color: '#FF7139' },
    { label: 'Edge', value: 320000, color: '#0078D4' },
    { label: 'Others', value: 220000, color: '#6B7280' },
  ],
};

// Dashboard content component
function DashboardContent() {
  const { selectedTenant, tenantId } = useAdminTenant();
  const [visitorPeriod, setVisitorPeriod] =
    React.useState<keyof typeof visitorData>('7days');
  const [browserPeriod, setBrowserPeriod] =
    React.useState<keyof typeof browserData>('7days');

  // Enhanced KPI data
  const {
    data: kpiData,
    loading: kpiLoading,
    error: kpiError,
    refresh: refreshKPI,
  } = useKPIData({
    autoRefresh: true,
    refreshInterval: 30000, // 30 seconds
  });

  // Analytics data
  const {
    data: analyticsData,
    loading: analyticsLoading,
    error: analyticsError,
    timeRange: analyticsTimeRange,
    setTimeRange: setAnalyticsTimeRange,
    refresh: refreshAnalytics,
  } = useAnalytics({
    timeRange: '30d',
    autoRefresh: true,
    refreshInterval: 5 * 60 * 1000, // 5 minutes
  });
  const [dashboardStats, setDashboardStats] = useState<StatCard[]>([]);
  // Analytics data fields
  const [revenueTrend, setRevenueTrend] = useState<any[]>([]);
  const [topProductsData, setTopProductsData] = useState<any[]>([]);
  const [recentOrderData, setRecentOrderData] = useState<any[]>([]);
  const [refundRate, setRefundRate] = useState<any[]>([]);
  const [customerSplit, setCustomerSplit] = useState<any[]>([]);
  const { isLoading } = useOptimizedLoading();

  // Listen for tenant changes and refresh data
  useAdminTenantListener(tenant => {
    console.log(`🔄 Dashboard: Refreshing data for tenant ${tenant.name}`);
    // Trigger data refresh when tenant changes
    refreshAnalytics();
  });

  console.log('analyticsData:::::::', { analyticsData, kpiData });
  // Fetch analytics data
  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        console.log('[AdminDashboard] Fetching analytics from backend API...');

        const response = await medusaAdminAPI.getDashboardAnalytics();
        console.log('Analytics API response:', response);

        if (response && response?.stats) {
          // Update stats with real data
          const updatedStats = [
            {
              name: 'Total Revenue',
              value: `₹${response?.stats?.totalRevenue.toLocaleString(
                'en-IN'
              )}`,
              change: `${
                response?.stats?.revenueGrowth > 0 ? '+' : ''
              }${response.stats.revenueGrowth?.toFixed(1)}%`,
              changeType:
                response?.stats?.revenueGrowth >= 0
                  ? ('increase' as const)
                  : ('decrease' as const),
              icon: CurrencyDollarIcon,
            },
            {
              name: 'Total Orders',
              value: response?.stats?.totalOrders.toLocaleString('en-IN'),
              change: `${
                response?.stats?.orderGrowth > 0 ? '+' : ''
              }${response?.stats?.orderGrowth?.toFixed(1)}%`,
              changeType:
                response?.stats?.orderGrowth >= 0
                  ? ('increase' as const)
                  : ('decrease' as const),
              icon: ClipboardDocumentListIcon,
            },
            {
              name: 'Total Customers',
              value: response?.stats?.totalCustomers.toLocaleString('en-IN'),
              change: `${
                response?.stats?.customerGrowth > 0 ? '+' : ''
              }${response?.stats?.customerGrowth?.toFixed(1)}%`,
              changeType:
                response?.stats?.customerGrowth >= 0
                  ? ('increase' as const)
                  : ('decrease' as const),
              icon: UsersIcon,
            },
            {
              name: 'Avg Order Value',
              value: `₹${response?.stats?.averageOrderValue.toLocaleString(
                'en-IN'
              )}`,
              change: `${response?.stats?.averageOrderValue > 0 ? '+' : ''}0%`,
              changeType: 'increase' as const,
              icon: ShoppingBagIcon,
            },
          ];
          console.log('updatedStats::::::::::', updatedStats);

          setDashboardStats(updatedStats);

          // Set analytics chart/table data from API response
          setRevenueTrend(response.revenueTrend || []);
          setTopProductsData(response.topProducts || []);
          setRecentOrderData(response.topOrders || []);
          setRefundRate(response.refundRate || []);
          setCustomerSplit(response.customerSplit || []);
        }
      } catch (error) {
        console.error('[AdminDashboard] Error fetching analytics:', error);
        // Keep using mock data on error
      }
    };

    fetchAnalytics();
  }, []);

  return (
    <div className='space-y-6'>
      {/* Page header */}
      <div className='md:flex md:items-center md:justify-between px-4 sm:px-6 md:px-8'>
        <div className='flex-1 min-w-0'>
          <h2 className='text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate'>
            Dashboard
          </h2>
          <p className='mt-1 text-sm text-gray-500'>
            Welcome back! Here's what's happening with your store today.
          </p>
        </div>
        <div className='mt-4 flex items-center gap-3 md:mt-0 md:ml-4'>
          <NotificationSystem />
        </div>
      </div>

      {/* Visual Separator */}
      <div className='px-4 sm:px-6 md:px-8'>
        <hr className='border-gray-200 opacity-60' />
      </div>

      {/* Enhanced KPI Dashboard */}
      <div className='px-4 sm:px-6 md:px-8'>
        <MUIKPIDashboard data={kpiData} loading={kpiLoading} className='mb-8' />

        {/* Error handling for KPI data */}
        {kpiError && (
          <div className='mb-6 p-4 bg-red-50 border border-red-200 rounded-lg'>
            <div className='flex'>
              <div className='flex-shrink-0'>
                <ExclamationTriangleIcon className='h-5 w-5 text-red-400' />
              </div>
              <div className='ml-3'>
                <h3 className='text-sm font-medium text-red-800'>
                  KPI Data Error
                </h3>
                <div className='mt-2 text-sm text-red-700'>
                  <p>{kpiError}</p>
                </div>
                <div className='mt-4'>
                  <button
                    type='button'
                    className='bg-red-100 px-2 py-1 rounded-md text-sm font-medium text-red-800 hover:bg-red-200'
                    onClick={refreshKPI}
                  >
                    Retry
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Advanced Analytics Charts */}
      <div className='px-4 sm:px-6 md:px-8 mb-8'>
        <AdvancedAnalyticsCharts
          timeRange={analyticsTimeRange as '7d' | '30d' | '90d' | '1y'}
          onTimeRangeChange={range =>
            setAnalyticsTimeRange(range as '7d' | '30d' | '90d' | '1y')
          }
          className='mb-8'
        />

        {/* Error handling for Analytics data */}
        {analyticsError && (
          <div className='mb-6 p-4 bg-red-50 border border-red-200 rounded-lg'>
            <div className='flex'>
              <div className='flex-shrink-0'>
                <ExclamationTriangleIcon className='h-5 w-5 text-red-400' />
              </div>
              <div className='ml-3'>
                <h3 className='text-sm font-medium text-red-800'>
                  Analytics Data Error
                </h3>
                <div className='mt-2 text-sm text-red-700'>
                  <p>{analyticsError}</p>
                </div>
                <div className='mt-4'>
                  <button
                    type='button'
                    className='bg-red-100 px-2 py-1 rounded-md text-sm font-medium text-red-800 hover:bg-red-200'
                    onClick={refreshAnalytics}
                  >
                    Retry
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Visual Separator */}
      <div className='px-4 sm:px-6 md:px-8'>
        <hr className='border-gray-200 opacity-60' />
      </div>

      {/* Quick Actions & Activity Feed Section */}
      <div className='px-4 sm:px-6 md:px-8 mb-8'>
        {/* Quick Actions */}
        <div className='mb-8'>
          <QuickActions className='h-full' />
        </div>

        {/* Activity Feed */}
        <div>
          <ActivityFeed
            className='h-full'
            maxItems={8}
            showFilters={true}
            autoRefresh={true}
            refreshInterval={30000}
          />
        </div>
      </div>

      <div className='grid grid-cols-1 gap-6 lg:grid-cols-2 px-4 sm:px-6 md:px-8 !mb-[30px]'>
        {/* Recent Orders */}
        <div className='bg-white shadow rounded-lg'>
          <div className='px-4 py-5 sm:p-6'>
            <div className='flex items-center justify-between mb-4'>
              <h3 className='text-lg leading-6 font-medium text-gray-900'>
                Recent Orders
              </h3>
              <Link
                href='/admin/orders'
                className='text-sm text-blue-600 hover:text-blue-500'
              >
                View all
              </Link>
            </div>
            <div className='flow-root'>
              <ul className='-my-5 divide-y divide-gray-200'>
                {recentOrderData.map(order => (
                  <li key={order.order_display_id} className='py-4'>
                    <div className='flex items-center space-x-4'>
                      <div className='flex-1 min-w-0'>
                        <p className='text-sm font-medium text-gray-900 truncate'>
                          {'#OD_' + order.order_display_id} -{' '}
                          {order.customer_name}
                        </p>
                        <p className='text-sm text-gray-500 truncate'>
                          {order.customer_email}
                        </p>
                      </div>
                      <div className='text-right'>
                        <p className='text-sm font-medium text-gray-900'>
                          {formatCurrency(order.total_order_amount)}
                        </p>
                        <p
                          className={`text-xs ${
                            order.order_status === 'Completed'
                              ? 'text-green-600'
                              : order.order_status === 'pending'
                                ? 'text-yellow-600'
                                : order.order_status === 'Shipped'
                                  ? 'text-blue-600'
                                  : 'text-gray-600'
                          }`}
                        >
                          {order.order_status}
                        </p>
                      </div>
                      <div>
                        <Link
                          href={`/admin/orders/${order.order_id}`}
                          className='text-gray-400 hover:text-gray-500'
                        >
                          <EyeIcon className='h-5 w-5' />
                        </Link>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Top Products */}
        <div className='bg-white shadow rounded-lg'>
          <div className='px-4 py-5 sm:p-6'>
            <div className='flex items-center justify-between mb-4'>
              <h3 className='text-lg leading-6 font-medium text-gray-900'>
                Top Products
              </h3>
              <Link
                href='/admin/products'
                className='text-sm text-blue-600 hover:text-blue-500'
              >
                View all
              </Link>
            </div>
            <div className='flow-root'>
              {analyticsLoading ? (
                <div className='flex items-center justify-center h-24 text-gray-400'>
                  Loading...
                </div>
              ) : (
                <ul className='-my-5 divide-y divide-gray-200'>
                  {topProductsData.map(product => (
                    <li
                      key={product.productId}
                      className='py-4 flex items-center space-x-4'
                    >
                      <div className='flex items-center space-x-4'>
                        <div className='flex-shrink-0'>
                          <div className='relative h-10 w-10 rounded-lg overflow-hidden bg-gray-100 flex items-center justify-center'>
                            <Image
                              src='/images/products/placeholder.svg'
                              alt={product.title}
                              width={40}
                              height={40}
                              className='object-cover rounded-lg'
                              priority={false}
                            />
                          </div>
                        </div>
                      </div>
                      <div className='flex-1 min-w-0'>
                        <p className='text-sm font-medium text-gray-900 truncate'>
                          {product.title}
                        </p>
                        <p className='text-sm text-gray-500'>
                          {product.units} sales • Stock: {product.stock || 0}
                        </p>
                      </div>
                      <div className='text-right'>
                        <p className='text-sm font-medium text-gray-900'>
                          {formatCurrency(product.gross) || 0}
                        </p>
                      </div>
                    </li>
                  ))}
                </ul>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Main dashboard component with loading wrapper
export default function AdminDashboard() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const { showLoading, hideLoading } = useLoadingBackdrop();

  // Redirect to store-specific admin route
  useEffect(() => {
    hideLoading();
    if (isLoading) return; // Wait for auth to load

    if (!isAuthenticated) {
      // Redirect unauthenticated users to login
      router.push('/');
      return;
    }

    if (user) {
      // Generate store handle and redirect to store-specific admin
      const storeHandle =
        (user as any)?.store_handle || `store-${user.email.split('@')[0]}`;
      router.push(`/${storeHandle}/admin`);
    }
  }, [isAuthenticated, user, isLoading, router]);

  // Show loading while redirecting
  // if (isLoading || !isAuthenticated) {
  //   return <LoadingBackdrop open={true} />;
  // }

  // This should not be reached due to redirect, but keep as fallback
  return <DashboardContent />;
}
