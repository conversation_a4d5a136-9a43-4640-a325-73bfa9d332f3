'use client';

import React, { useCallback, useMemo, useState } from 'react';
import Link from 'next/link';

/** Tiny inline SVGs (faster than importing an icon package) */
const SvgArrowLeft = () => (
  <svg width='16' height='16' viewBox='0 0 24 24' aria-hidden>
    <path
      fill='currentColor'
      d='M20 11H7.83l5.59-5.59L12 4l-8 8l8 8l1.41-1.41L7.83 13H20v-2Z'
    />
  </svg>
);
const SvgMail = () => (
  <svg width='20' height='20' viewBox='0 0 24 24' aria-hidden>
    <path
      fill='currentColor'
      d='M20 4H4a2 2 0 0 0-2 2v12c0 1.1.9 2 2 2h16a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2Zm0 4l-8 5L4 8V6l8 5l8-5v2Z'
    />
  </svg>
);
const SvgCheckCircle = () => (
  <svg width='64' height='64' viewBox='0 0 24 24' aria-hidden>
    <path
      fill='#22c55e'
      d='M12 2a10 10 0 1 0 10 10A10.011 10.011 0 0 0 12 2Zm-1 15l-5-5l1.41-1.41L11 14.17l6.59-6.59L19 9Z'
    />
  </svg>
);

const Spinner = () => (
  <span className='inline-block h-4 w-4 animate-spin rounded-full border-2 border-white border-b-transparent align-middle' />
);

const SuccessView = React.memo(function SuccessView({
  email,
  onTryAnother,
}: {
  email: string;
  onTryAnother: () => void;
}) {
  return (
    <div className='min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8'>
      <div className='sm:mx-auto sm:w-full sm:max-w-md'>
        <div className='bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10'>
          <div className='text-center'>
            <div className='w-16 h-16 mx-auto mb-4'>
              <SvgCheckCircle />
            </div>
            <h2 className='text-2xl font-bold text-gray-900 mb-2'>
              Check your email
            </h2>
            <p className='text-gray-600 mb-6'>
              We&apos;ve sent a password reset link to <strong>{email}</strong>
            </p>
            <p className='text-sm text-gray-500 mb-6'>
              Didn&apos;t receive the email? Check your spam folder or try
              again.
            </p>

            <div className='space-y-3'>
              <button
                onClick={onTryAnother}
                className='w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
              >
                Try another email
              </button>

              <Link
                href='/'
                prefetch={false}
                className='w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
              >
                Back to sign in
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

function ForgotPasswordForm() {
  const [email, setEmail] = useState('');
  const [error, setError] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  // cheap, synchronous email check to avoid pointless submits
  const isEmailValid = useMemo(() => {
    const v = email.trim();
    if (!v) return false;
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v);
  }, [email]);

  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();
      if (!isEmailValid) return;

      setIsLoading(true);
      setError('');

      try {
        // Replace with your real API call:
        // await api.requestPasswordReset(email);
        await new Promise(r => setTimeout(r, 300)); // keep the demo delay tiny
        setIsSubmitted(true);
      } catch {
        setError('Failed to send reset email. Please try again.');
      } finally {
        setIsLoading(false);
      }
    },
    [isEmailValid]
  );

  if (isSubmitted) {
    return (
      <SuccessView
        email={email}
        onTryAnother={() => {
          setIsSubmitted(false);
          setEmail('');
        }}
      />
    );
  }

  return (
    <div className=' bg-gray-50 flex flex-col justify-center py-12 '>
      <div className='px-12'>
        <div className='text-center'>
          <h2 className='mt-6 text-3xl font-extrabold text-gray-900'>
            Forgot your password?
          </h2>
          <p className='mt-2 text-sm text-gray-600'>
            Enter your email address and we&apos;ll send you a link to reset
            your password.
          </p>
        </div>
      </div>

      <div className='mt-8 px-12'>
        <div className='bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10'>
          <form className='space-y-6' onSubmit={handleSubmit} noValidate>
            <div>
              <label
                htmlFor='email'
                className='block text-sm font-medium text-gray-700'
              >
                Email address
              </label>
              <div className='mt-1 relative'>
                <div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
                  <SvgMail />
                </div>
                <input
                  id='email'
                  name='email'
                  type='email'
                  inputMode='email'
                  autoComplete='email'
                  required
                  autoFocus
                  value={email}
                  onChange={e => setEmail(e.target.value)}
                  className='appearance-none block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'
                  placeholder='Enter your email'
                  aria-invalid={!!error || (!isEmailValid && !!email)}
                  aria-describedby={error ? 'error-text' : undefined}
                />
              </div>
            </div>

            {error && (
              <div
                id='error-text'
                className='rounded-md bg-red-50 p-3 text-sm text-red-700'
              >
                {error}
              </div>
            )}

            <div>
              <button
                type='submit'
                disabled={isLoading || !isEmailValid}
                className='w-full flex justify-center items-center gap-2 py-2 px-4 rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed'
              >
                {isLoading ? (
                  <>
                    <Spinner /> Sending...
                  </>
                ) : (
                  'Send reset link'
                )}
              </button>
            </div>
          </form>

          <div className='mt-6'>
            <Link
              href='/auth/login'
              prefetch={false}
              className='flex items-center justify-center text-sm text-blue-600 hover:text-blue-500'
            >
              <span className='mr-1 inline-flex'>
                <SvgArrowLeft />
              </span>
              Back to sign in
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}

export default React.memo(ForgotPasswordForm);
