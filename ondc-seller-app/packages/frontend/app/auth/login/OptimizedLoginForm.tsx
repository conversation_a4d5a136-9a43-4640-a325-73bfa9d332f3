'use client';

import React, { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useMutation } from '@tanstack/react-query';
import {
  Box,
  Button,
  Alert,
  Typography,
  Paper,
  Container,
  Stack,
  FormControlLabel,
  Checkbox,
} from '@mui/material';
import { useAuth } from '@/contexts/AuthContext';
import { useAuthStore } from '@/stores/authStore';
import <PERSON>i<PERSON><PERSON><PERSON>ield from '@/components/admin/MuiFormField';
import { FormErrorBoundary } from '@/components/admin/ErrorBoundary';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { loginSchema, LoginFormValues } from './validations';
import { authAPI } from '@/lib/api/auth';

/**
 * Optimized Login Form using TanStack Query
 * - No API calls on component mount
 * - API calls only triggered by user actions (login button click)
 * - Memoized event handlers to prevent unnecessary re-renders
 * - Performance monitoring integration
 */
const OptimizedLoginForm = React.memo(() => {
  const router = useRouter();
  const { setAuth } = useAuthStore();
  const { login } = useAuth();

  const [rememberMe, setRememberMe] = useState(false);

  const { register, handleSubmit, formState, setError } =
    useForm<LoginFormValues>({
      resolver: zodResolver(loginSchema),
      mode: 'onBlur',
      defaultValues: { email: '', password: '' },
    });

  // ✅ OPTIMIZATION: On-demand login API call with TanStack Query
  const loginMutation = useMutation({
    mutationFn: async (credentials: LoginFormValues) => {
      const startTime = performance.now();

      try {
        // const response = await authAPI.login({
        //   email: credentials.email,
        //   password: credentials.password,
        //   remember_me: rememberMe,
        // });
        const response: any = await login({
          email: credentials.email,
          password: credentials.password,
          // remember_me: rememberMe,
        });
        console.log('===========response==========', response);
        // Track API performance
        const duration = performance.now() - startTime;
        if (process.env.NODE_ENV === 'development') {
          console.log(
            `🚀 Login API call completed in ${duration.toFixed(2)}ms`
          );
        }

        return response;
      } catch (error) {
        const duration = performance.now() - startTime;
        if (process.env.NODE_ENV === 'development') {
          console.error(
            `❌ Login API call failed after ${duration.toFixed(2)}ms:`,
            error
          );
        }
        throw error;
      }
    },
    onSuccess: response => {
      // Update auth store only on successful login
      setAuth({
        user: response.data.user,
        token: response.data.token,
        refreshToken: response.data.refresh_token,
        isAuthenticated: true,
      });

      // Navigate based on user role and onboarding status
      if (response.data.user.role === 'admin') {
        if (response.data.user.onboarding_status !== 'completed') {
          // Redirect to onboarding if not completed
          router.push('/onboarding');
        } else {
          // Redirect to admin dashboard
          router.push(`/${response.data.user.store_handle}/admin`);
        }
      } else {
        // Regular user redirect
        router.push('/');
      }
    },
    onError: (error: any) => {
      // Handle login errors without affecting other components
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        'Login failed. Please check your credentials.';

      setError('root', {
        type: 'server',
        message: errorMessage,
      });
    },
  });

  // ✅ OPTIMIZATION: Memoized submit handler to prevent re-renders
  const onSubmit = useCallback(
    async (values: LoginFormValues) => {
      // Clear any previous errors
      setError('root', { type: 'server', message: '' });

      const response: any = await login({
        email: values.email,
        password: values.password,
        // remember_me: rememberMe,
      });
      console.log('===========response==========', response);
      return;
      setAuth({
        user: response.data.user,
        token: response.data.token,
        refreshToken: response.data.refresh_token,
        isAuthenticated: true,
      });

      if (response.data.user.role === 'admin') {
        if (response.data.user.onboarding_status !== 'completed') {
          // Redirect to onboarding if not completed
          router.push('/onboarding');
        } else {
          // Redirect to admin dashboard
          router.push(`/${response.data.user.store_handle}/admin`);
        }
      }

      // Trigger API call only when user clicks login
      // loginMutation.mutate(values);
    },
    [setError]
  );

  // ✅ OPTIMIZATION: Memoized checkbox handler
  const handleRememberMeChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setRememberMe(event.target.checked);
    },
    []
  );

  const { errors, isSubmitting } = formState;
  const isLoading = loginMutation.isPending || isSubmitting;

  return (
    <FormErrorBoundary>
      <Container className='!p-0'>
        <Paper elevation={1} sx={{ p: 4, mt: 8 }}>
          <Typography
            variant='h4'
            align='center'
            className='text-4xl font-extrabold pb-2'
          >
            OneStore
          </Typography>
          <Typography variant='h5' align='center' gutterBottom>
            Sign In
          </Typography>

          <Box component='form' onSubmit={onSubmit} sx={{ mt: 3 }}>
            {/* Display API errors */}
            {(errors.root?.message || loginMutation.error) && (
              <Alert severity='error' sx={{ mb: 2 }}>
                {errors.root?.message || 'Login failed. Please try again.'}
              </Alert>
            )}

            <Stack spacing={3}>
              <MuiFormField
                label='Email Address'
                name='email'
                type='email'
                register={register}
                error={errors.email}
                required
                autoComplete='email'
                autoFocus
              />

              <MuiFormField
                label='Password'
                name='password'
                type='password'
                register={register}
                error={errors.password}
                required
                autoComplete='current-password'
              />

              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}
              >
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={rememberMe}
                      onChange={handleRememberMeChange}
                      name='rememberMe'
                    />
                  }
                  label='Remember me'
                />

                <Link
                  href='/auth/forgot-password'
                  prefetch={false}
                  style={{ textDecoration: 'none' }}
                >
                  <Typography
                    variant='body2'
                    color='primary'
                    sx={{ cursor: 'pointer' }}
                  >
                    Forgot password?
                  </Typography>
                </Link>
              </Box>

              <Button
                type='submit'
                fullWidth
                variant='contained'
                size='large'
                disabled={isLoading}
                sx={{ mt: 3, mb: 2 }}
              >
                {isLoading ? 'Signing in...' : 'SIGN IN'}
              </Button>
            </Stack>

            <Box sx={{ textAlign: 'center', mt: 2 }}>
              <Typography variant='body2' color='text.secondary'>
                Don't have an account?{' '}
                <Link
                  href='/auth/register'
                  prefetch={false}
                  style={{ textDecoration: 'none' }}
                >
                  <Typography
                    component='span'
                    variant='body2'
                    color='primary'
                    sx={{ cursor: 'pointer' }}
                  >
                    Sign up here
                  </Typography>
                </Link>
              </Typography>
            </Box>
          </Box>
        </Paper>
      </Container>
    </FormErrorBoundary>
  );
});

OptimizedLoginForm.displayName = 'OptimizedLoginForm';

export default OptimizedLoginForm;
