// 'use client';

// import React, { useState } from 'react';
// import { useRouter } from 'next/navigation';
// import Link from 'next/link';
// import {
//   Box,
//   Button,
//   Alert,
//   Typography,
//   Paper,
//   Container,
//   Divider,
//   Stack,
//   FormControlLabel,
//   Checkbox,
// } from '@mui/material';
// import { useAuth } from '@/contexts/AuthContext';
// import MuiFormField from '@/components/admin/MuiFormField';
// import { FormErrorBoundary } from '@/components/admin/ErrorBoundary';
// import { useFormValidation } from '@/hooks/useFormValidation';
// import { FIELD_VALIDATION_RULES } from '@/lib/validation';

// const LoginPage = () => {
//   const [rememberMe, setRememberMe] = useState(false);
//   const { login } = useAuth();

//   const router = useRouter();

//   // Form validation with specific rules for login
//   const {
//     data,
//     errors,
//     isSubmitting,
//     handleChange,
//     handleBlur,
//     handleSubmit,
//     setFieldError,
//     clearErrors,
//   } = useFormValidation({
//     initialData: {
//       email: '',
//       password: '',
//     },
//     validationRules: {
//       email:
//         process.env.NODE_ENV === 'development'
//           ? { required: true } // Allow any format in development
//           : FIELD_VALIDATION_RULES.email, // Strict email validation in production
//       password: {
//         required: true,
//         minLength: process.env.NODE_ENV === 'development' ? 4 : 6, // Allow shorter passwords in development
//       },
//     },
//     onSubmit: async formData => {
//       try {
//         // Login and get onboarding status
//         const loginResult = await login({
//           email: formData.email,
//           password: formData.password,
//         });
//         // Redirect based on onboarding status
//         if (loginResult.user.role === 'admin') {
//           if (loginResult.user.onboarding_status !== 'completed') {
//             console.log(
//               '📋 Redirecting to onboarding at step:',
//               loginResult.currentStep
//             );
//             router.push(loginResult.redirectPath);
//           } else {
//             console.log(
//               '✅ Onboarding completed, redirecting to admin dashboard:',
//               loginResult.redirectPath
//             );
//             router.push(`/${loginResult?.user?.store_handle}/admin`);
//           }
//         } else {
//           console.log('customer');
//           router.push('/');
//         }
//       } catch (err) {
//         const errorMessage =
//           err instanceof Error ? err.message : 'Login failed';
//         setFieldError('submit', errorMessage);
//         throw err; // Re-throw to prevent form submission success
//       }
//     },
//     onError: formErrors => {
//       console.error('Login form validation errors:', formErrors);
//     },
//   });

//   return (
//     <FormErrorBoundary>
//       <Container className='!p-0'>
//         <Paper elevation={3} sx={{ p: 4, mt: 8 }}>
//           <Typography
//             variant='h4'
//             component='h1'
//             align='center'
//             className='text-4xl font-extrabold pb-2'
//           >
//             OneStore
//           </Typography>
//           <Typography variant='h5' component='h1' align='center' gutterBottom>
//             Sign In
//           </Typography>

//           <Box component='form' onSubmit={handleSubmit} sx={{ mt: 3 }}>
//             {errors.submit && (
//               <Alert severity='error' sx={{ mb: 2 }}>
//                 {errors.submit}
//               </Alert>
//             )}

//             <Stack spacing={3}>
//               <MuiFormField
//                 label={
//                   process.env.NODE_ENV === 'development'
//                     ? 'Username/Email'
//                     : 'Email Address'
//                 }
//                 name='email'
//                 type={process.env.NODE_ENV === 'development' ? 'text' : 'email'}
//                 value={data.email}
//                 onChange={handleChange}
//                 onBlur={handleBlur}
//                 placeholder={
//                   process.env.NODE_ENV === 'development'
//                     ? 'Enter username or email'
//                     : 'Enter your email'
//                 }
//                 required
//                 error={errors.email}
//                 disabled={isSubmitting}
//                 validationRules={
//                   process.env.NODE_ENV === 'development'
//                     ? { required: true }
//                     : FIELD_VALIDATION_RULES.email
//                 }
//               />

//               <MuiFormField
//                 label='Password'
//                 name='password'
//                 type='password'
//                 value={data.password}
//                 onChange={handleChange}
//                 onBlur={handleBlur}
//                 placeholder='Enter your password'
//                 required
//                 error={errors.password}
//                 disabled={isSubmitting}
//                 validationRules={{
//                   required: true,
//                   minLength: process.env.NODE_ENV === 'development' ? 4 : 6,
//                 }}
//               />

//               <Box
//                 sx={{
//                   display: 'flex',
//                   justifyContent: 'space-between',
//                   alignItems: 'center',
//                 }}
//               >
//                 <FormControlLabel
//                   control={
//                     <Checkbox
//                       checked={rememberMe}
//                       onChange={e => setRememberMe(e.target.checked)}
//                       name='rememberMe'
//                       color='primary'
//                     />
//                   }
//                   label='Remember me'
//                 />

//                 <Link
//                   href='/auth/forgot-password'
//                   style={{ textDecoration: 'none' }}
//                 >
//                   <Typography
//                     variant='body2'
//                     color='primary'
//                     sx={{ cursor: 'pointer' }}
//                   >
//                     Forgot password?
//                   </Typography>
//                 </Link>
//               </Box>

//               <Button
//                 type='submit'
//                 fullWidth
//                 variant='contained'
//                 size='large'
//                 disabled={isSubmitting}
//                 sx={{ mt: 3, mb: 2 }}
//               >
//                 {isSubmitting ? 'Signing in...' : 'SIGN IN'}
//               </Button>
//             </Stack>

//             <Box sx={{ textAlign: 'center', mt: 3 }}>
//               <Typography variant='body2' color='text.secondary'>
//                 {"Don't have an account?"}
//                 <Link href='/auth/register' style={{ textDecoration: 'none' }}>
//                   <Typography
//                     component='span'
//                     variant='body2'
//                     color='primary'
//                     sx={{ cursor: 'pointer' }}
//                   >
//                     Sign up
//                   </Typography>
//                 </Link>
//               </Typography>
//             </Box>
//           </Box>
//         </Paper>
//       </Container>
//     </FormErrorBoundary>
//   );
// };

// export default LoginPage;

import { FEATURE_FLAGS } from '@/lib/config/featureFlags';
import ClientLoginForm from './ClientLoginForm';
import OptimizedLoginForm from './OptimizedLoginForm';

export default function Page() {
  // Use optimized version when feature flag is enabled
  // if (FEATURE_FLAGS.USE_TANSTACK_QUERY_AUTH) {
  //   return <OptimizedLoginForm />;
  // }
  return <OptimizedLoginForm />;

  // Fallback to original implementation
  // return <ClientLoginForm />;
}
