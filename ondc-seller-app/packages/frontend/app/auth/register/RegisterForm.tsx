'use client';

import React, { useEffect, useMemo, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  Box,
  Button,
  Alert,
  Typography,
  Paper,
  Container,
  FormControlLabel,
  Checkbox,
  TextField,
  InputAdornment,
  IconButton,
  Stack,
  CircularProgress,
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Person,
  Store,
  Email,
  Phone,
  Lock,
  CheckCircle,
  Error as ErrorIcon,
} from '@mui/icons-material';
import { useAuth } from '@/contexts/AuthContext';
import { FormErrorBoundary } from '@/components/admin/ErrorBoundary';
import { useToast } from '@/components/common/ToastProvider';
import { useLoadingBackdrop } from '@/contexts/LoadingBackdropContext';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { registerSchema, RegisterFormValues } from './validations';

export default function RegisterForm() {
  const toast = useToast();
  const router = useRouter();
  const { registerTenant } = useAuth();
  const { hideLoading } = useLoadingBackdrop();

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // RHF + Zod setup
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError,
    watch,
    reset,
    setValue,
  } = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    mode: 'onBlur', // validate on blur to keep typing smooth
    defaultValues: {
      firstName: '',
      lastName: '',
      storeName: '',
      storeHandle: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
      acceptTerms: false,
    },
  });

  // Auto-generate storeHandle from storeName (lowercase, hyphens)
  const storeName = watch('storeName');
  useEffect(() => {
    if (!storeName) return;
    const suggested = storeName
      .toLowerCase()
      .trim()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
    setValue('storeHandle', suggested, { shouldValidate: true });
  }, [storeName, setValue]);

  useEffect(() => {
    hideLoading();
  }, [hideLoading]);

  const onSubmit = useCallback(
    async (values: RegisterFormValues) => {
      try {
        // registerTenant expects these exact keys (adjust if your API differs)

        const response = await registerTenant({
          firstName: values.firstName,
          lastName: values.lastName,
          email: values.email,
          phone: values.phone,
          storeName: values.storeName,
          storeHandle: values.storeHandle,
          password: values.password,
        });
        if (response?.user?.id) {
          reset();
          toast.success('Account created successfully!');
          router.push('/');
        }
      } catch (e: any) {
        const message = e?.message || 'Registration failed';
        // surface as form-level error and toast
        setError('root', { type: 'server', message });
        console.log('root', { type: 'server', message });
        toast.error(`Registration failed!`);
      }
    },
    [registerTenant, reset, router, setError, toast]
  );

  const submitError = errors.root?.message;

  return (
    <FormErrorBoundary>
      <Container maxWidth='sm' sx={{ py: 4 }}>
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <Typography
            variant='h4'
            component='h1'
            gutterBottom
            fontWeight='bold'
          >
            Create Account
          </Typography>
          <Typography variant='body1' color='text.secondary'>
            Join our platform and start your journey
          </Typography>
        </Box>

        <Box component='form' onSubmit={handleSubmit(onSubmit)} noValidate>
          <Paper elevation={1} sx={{ p: 3 }}>
            <Stack spacing={3}>
              {/* First & Last Name */}
              <Box
                sx={{
                  display: 'flex',
                  gap: 2,
                  flexDirection: { xs: 'column', sm: 'row' },
                }}
              >
                <TextField
                  fullWidth
                  label='First Name'
                  placeholder='Enter your first name'
                  aria-label='First Name'
                  autoComplete='given-name'
                  disabled={isSubmitting}
                  {...register('firstName')}
                  error={!!errors.firstName}
                  helperText={errors.firstName?.message}
                  slotProps={{
                    input: {
                      startAdornment: (
                        <InputAdornment position='start'>
                          <Person color='action' />
                        </InputAdornment>
                      ),
                    },
                  }}
                />

                <TextField
                  fullWidth
                  label='Last Name'
                  placeholder='Enter your last name'
                  aria-label='Last Name'
                  autoComplete='family-name'
                  disabled={isSubmitting}
                  {...register('lastName')}
                  error={!!errors.lastName}
                  helperText={errors.lastName?.message}
                  slotProps={{
                    input: {
                      startAdornment: (
                        <InputAdornment position='start'>
                          <Person color='action' />
                        </InputAdornment>
                      ),
                    },
                  }}
                />
              </Box>

              {/* Store Name */}
              <TextField
                fullWidth
                label='Store Name'
                placeholder='Enter your store name'
                aria-label='Store Name'
                disabled={isSubmitting}
                {...register('storeName')}
                error={!!errors.storeName}
                helperText={errors.storeName?.message}
                slotProps={{
                  input: {
                    startAdornment: (
                      <InputAdornment position='start'>
                        <Store color='action' />
                      </InputAdornment>
                    ),
                  },
                }}
              />

              {/* Store Handle */}
              <TextField
                fullWidth
                label='Store Handle'
                placeholder='your-store-handle'
                aria-label='Store Handle'
                disabled={isSubmitting}
                {...register('storeHandle')}
                error={!!errors.storeHandle}
                helperText={
                  errors.storeHandle?.message ||
                  'Auto-generated from store name (lowercase, hyphens only)'
                }
                slotProps={{
                  input: {
                    startAdornment: (
                      <InputAdornment position='start'>
                        <Store color='action' />
                      </InputAdornment>
                    ),
                  },
                }}
              />

              {/* Email */}
              <TextField
                fullWidth
                label='Email Address'
                type='email'
                placeholder='Enter your email address'
                aria-label='Email Address'
                autoComplete='email'
                disabled={isSubmitting}
                {...register('email')}
                error={!!errors.email}
                helperText={errors.email?.message}
                slotProps={{
                  input: {
                    startAdornment: (
                      <InputAdornment position='start'>
                        <Email color='action' />
                      </InputAdornment>
                    ),
                  },
                }}
              />

              {/* Phone */}
              <TextField
                fullWidth
                label='Contact Number'
                type='tel'
                placeholder='Enter your phone number'
                aria-label='Contact Number'
                autoComplete='tel'
                disabled={isSubmitting}
                {...register('phone')}
                error={!!errors.phone}
                helperText={errors.phone?.message}
                slotProps={{
                  input: {
                    startAdornment: (
                      <InputAdornment position='start'>
                        <Phone color='action' />
                      </InputAdornment>
                    ),
                  },
                }}
              />

              {/* Password */}
              <TextField
                fullWidth
                label='Password'
                type={showPassword ? 'text' : 'password'}
                placeholder='Create a strong password'
                aria-label='Password'
                autoComplete='new-password'
                disabled={isSubmitting}
                {...register('password')}
                error={!!errors.password}
                helperText={
                  errors.password?.message ||
                  'Must contain uppercase, lowercase, number, and special character'
                }
                slotProps={{
                  input: {
                    startAdornment: (
                      <InputAdornment position='start'>
                        <Lock color='action' />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position='end'>
                        <IconButton
                          aria-label='toggle password visibility'
                          onClick={() => setShowPassword(v => !v)}
                          edge='end'
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  },
                }}
              />

              {/* Confirm Password */}
              <TextField
                fullWidth
                label='Confirm Password'
                type={showConfirmPassword ? 'text' : 'password'}
                placeholder='Confirm your password'
                aria-label='Confirm Password'
                autoComplete='new-password'
                disabled={isSubmitting}
                {...register('confirmPassword')}
                error={!!errors.confirmPassword}
                helperText={errors.confirmPassword?.message}
                slotProps={{
                  input: {
                    startAdornment: (
                      <InputAdornment position='start'>
                        <Lock color='action' />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position='end'>
                        <IconButton
                          aria-label='toggle confirm password visibility'
                          onClick={() => setShowConfirmPassword(v => !v)}
                          edge='end'
                        >
                          {showConfirmPassword ? (
                            <VisibilityOff />
                          ) : (
                            <Visibility />
                          )}
                        </IconButton>
                      </InputAdornment>
                    ),
                  },
                }}
              />

              {/* Terms & Conditions */}
              <FormControlLabel
                control={
                  <Checkbox
                    disabled={isSubmitting}
                    {...register('acceptTerms')}
                    color='primary'
                  />
                }
                label={
                  <Typography variant='body2' color='text.secondary'>
                    I agree to the{' '}
                    <Link
                      href='/terms'
                      prefetch={false}
                      style={{ color: 'inherit', textDecoration: 'underline' }}
                    >
                      Terms and Conditions
                    </Link>{' '}
                    and{' '}
                    <Link
                      href='/privacy'
                      prefetch={false}
                      style={{ color: 'inherit', textDecoration: 'underline' }}
                    >
                      Privacy Policy
                    </Link>
                  </Typography>
                }
                sx={{ mt: 1 }}
              />
              {errors.acceptTerms?.message && (
                <Typography variant='caption' color='error'>
                  {errors.acceptTerms.message}
                </Typography>
              )}

              {/* Submit */}
              <Button
                type='submit'
                fullWidth
                variant='contained'
                size='large'
                disabled={isSubmitting}
                sx={{
                  mt: 3,
                  py: 1.5,
                  fontSize: '1.1rem',
                  fontWeight: 'bold',
                  textTransform: 'uppercase',
                  borderRadius: 2,
                }}
                startIcon={
                  isSubmitting ? (
                    <CircularProgress size={20} color='inherit' />
                  ) : (
                    <CheckCircle />
                  )
                }
              >
                {isSubmitting ? 'Creating Account...' : 'Create Account'}
              </Button>
            </Stack>
          </Paper>

          <Box sx={{ textAlign: 'center', mt: 3 }}>
            <Typography variant='body2' color='text.secondary'>
              Already have an account?{' '}
              <Link
                href='/auth/login'
                prefetch={false}
                style={{ textDecoration: 'none' }}
              >
                <Typography
                  component='span'
                  variant='body2'
                  color='primary'
                  sx={{ cursor: 'pointer' }}
                >
                  Sign in
                </Typography>
              </Link>
            </Typography>
          </Box>
        </Box>
      </Container>
    </FormErrorBoundary>
  );
}
