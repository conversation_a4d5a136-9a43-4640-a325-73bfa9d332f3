import { z } from 'zod';

const passwordPolicy =
  /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[ !"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~]).{8,}$/;

export const registerSchema = z
  .object({
    firstName: z.string().min(1, 'First name is required'),
    lastName: z.string().min(1, 'Last name is required'),
    storeName: z.string().min(1, 'Store name is required'),
    storeHandle: z
      .string()
      .min(1, 'Store handle is required')
      .regex(/^[a-z0-9-]+$/, 'Lowercase letters, numbers and hyphens only'),
    email: z.string().email('Enter a valid email'),
    // phone: z
    //   .string()
    //   .min(7, 'Enter a valid phone')
    //   .regex(/^[0-9+\-() ]+$/, 'Enter a valid phone'),
    phone: z
      .string()
      .trim()
      .regex(/^\d{10}$/, 'Enter a valid phone.'),

    password: z
      .string()
      .regex(
        passwordPolicy,
        'Must contain uppercase, lowercase, number, and special character (min 8 chars)'
      ),
    confirmPassword: z.string().min(1, 'Please confirm your password'),
    acceptTerms: z.boolean().refine(v => v, {
      message: 'Please accept the terms and conditions',
    }),
  })
  .refine(d => d.password === d.confirmPassword, {
    path: ['confirmPassword'],
    message: 'Passwords do not match',
  });

export type RegisterFormValues = z.infer<typeof registerSchema>;
