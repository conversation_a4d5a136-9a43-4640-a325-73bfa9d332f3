import React, { lazy, Suspense } from 'react';
import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';

// 🚀 PERFORMANCE: Critical providers loaded immediately
import { AuthProvider } from '../contexts/AuthContext';
import { ToastProvider } from '../components/common/ToastProvider';
import { SWRProvider } from '../contexts/SWRProvider';
import { UnifiedAPIProvider } from '../contexts/UnifiedAPIProvider';
import LayoutWrapper from '../components/LayoutWrapper';

// 🚀 PERFORMANCE: Lazy load non-critical providers to reduce initial bundle
const CartProvider = lazy(() =>
  import('../contexts/CartContext').then(m => ({ default: m.CartProvider }))
);

const StoreConfigProvider = lazy(() =>
  import('../contexts/StoreConfigContext').then(m => ({
    default: m.StoreConfigProvider,
  }))
);
const MedusaCartProvider = lazy(() =>
  import('../hooks/useMedusaCart').then(m => ({
    default: m.MedusaCartProvider,
  }))
);
const StrapiProvider = lazy(() =>
  import('../contexts/StrapiProvider').then(m => ({
    default: m.StrapiProvider,
  }))
);
const ChannelProvider = lazy(() =>
  import('../contexts/ChannelContext').then(m => ({
    default: m.ChannelProvider,
  }))
);
const ChannelThemeProvider = lazy(() =>
  import('../contexts/ChannelContext').then(m => ({
    default: m.ChannelThemeProvider,
  }))
);
const CentralizedLoadingProvider = lazy(() =>
  import('../contexts/CentralizedLoadingContext').then(m => ({
    default: m.CentralizedLoadingProvider,
  }))
);
const LoadingBackdropProvider = lazy(() =>
  import('../contexts/LoadingBackdropContext').then(m => ({
    default: m.LoadingBackdropProvider,
  }))
);

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: {
    default: 'ONDC Seller Platform - Your Gateway to Digital Commerce',
    template: '%s | ONDC Seller Platform',
  },
  description:
    "Join the Open Network for Digital Commerce (ONDC) ecosystem. Discover products, manage your business, and connect with customers across India's digital marketplace.",
  keywords: [
    'ONDC',
    'Open Network Digital Commerce',
    'e-commerce',
    'digital marketplace',
    'online shopping',
    'seller platform',
    'India commerce',
    'digital payments',
    'online business',
  ],
  authors: [{ name: 'ONDC Seller Platform Team' }],
  creator: 'ONDC Seller Platform',
  publisher: 'ONDC Seller Platform',
  metadataBase: new URL(
    process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3001'
  ),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_IN',
    url: '/',
    title: 'ONDC Seller Platform - Your Gateway to Digital Commerce',
    description:
      "Join the Open Network for Digital Commerce (ONDC) ecosystem. Discover products, manage your business, and connect with customers across India's digital marketplace.",
    siteName: 'ONDC Seller Platform',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'ONDC Seller Platform',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'ONDC Seller Platform - Your Gateway to Digital Commerce',
    description:
      'Join the Open Network for Digital Commerce (ONDC) ecosystem. Discover products, manage your business, and connect with customers.',
    images: ['/og-image.jpg'],
    creator: '@ondcplatform',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
  },
};

// 🚀 PERFORMANCE: Optimized layout with lazy loading
const RootLayout = ({ children }: { children: React.ReactNode }) => (
  <html lang='en'>
    <body className={inter.className}>
      {/* 🚀 CRITICAL: Load essential providers immediately */}

      <UnifiedAPIProvider>
        <SWRProvider>
          <AuthProvider>
            <ToastProvider>
              {/* 🚀 PERFORMANCE: Lazy load non-critical providers */}
              {/* <Suspense
              fallback={
                <div className='min-h-screen bg-gray-50 animate-pulse' />
              }
            > */}
              <StrapiProvider>
                <StoreConfigProvider>
                  <CartProvider>
                    <MedusaCartProvider>
                      <CentralizedLoadingProvider>
                        <LoadingBackdropProvider>
                          <ChannelProvider>
                            <ChannelThemeProvider>
                              <LayoutWrapper>{children}</LayoutWrapper>
                            </ChannelThemeProvider>
                          </ChannelProvider>
                        </LoadingBackdropProvider>
                      </CentralizedLoadingProvider>
                    </MedusaCartProvider>
                  </CartProvider>
                </StoreConfigProvider>
              </StrapiProvider>
              {/* </Suspense> */}
            </ToastProvider>
          </AuthProvider>
        </SWRProvider>
      </UnifiedAPIProvider>
    </body>
  </html>
);

export default RootLayout;
