'use client';

/**
 * @deprecated This component is deprecated. Use ThemedStoreHeader from @/components/layouts/ThemedStoreHeader instead.
 * This component will be removed in a future version.
 */

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

interface HeaderProps {
  storeConfig?: any;
  storeHandle?: string;
}

const Header: React.FC<HeaderProps> = ({ storeConfig, storeHandle }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const router = useRouter();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      const searchUrl = storeHandle
        ? `/${storeHandle}/products?search=${encodeURIComponent(searchQuery)}`
        : `/products?search=${encodeURIComponent(searchQuery)}`;
      router.push(searchUrl);
    }
  };

  const navigationItems = storeHandle
    ? [
        { name: 'Home', href: `/${storeHandle}` },
        { name: 'Products', href: `/${storeHandle}/products` },
        { name: 'Categories', href: `/${storeHandle}/categories` },
        { name: 'Deals', href: `/${storeHandle}/deals` },
        { name: 'About', href: `/${storeHandle}/about` },
      ]
    : [
        { name: 'Dashboard', href: '/dashboard' },
        { name: 'Products', href: '/products' },
        { name: 'Orders', href: '/orders' },
        { name: 'Analytics', href: '/analytics' },
        { name: 'Settings', href: '/settings' },
      ];

  return (
    <header className='bg-store-background border-b border-store-border shadow-store'>
      <div className='px-4 sm:px-6 lg:px-8'>
        <div className='flex items-center justify-between h-16'>
          {/* Logo */}
          <div className='flex items-center'>
            <Link
              href={storeHandle ? `/${storeHandle}` : '/'}
              className='flex items-center space-x-2 store-focus-ring rounded-md p-1 -m-1'
            >
              {storeConfig?.store_logo ? (
                <img
                  src={storeConfig.store_logo}
                  alt={storeConfig.store_name}
                  className='w-8 h-8 rounded-lg object-cover shadow-store'
                />
              ) : (
                <div className='w-8 h-8 rounded-lg flex items-center justify-center bg-store-primary shadow-store'>
                  <span className='text-store-background font-bold text-sm'>
                    {storeConfig?.store_name?.[0] || 'O'}
                  </span>
                </div>
              )}
              <span className='text-xl font-bold text-store-text hover:text-store-primary transition-colors duration-200'>
                {storeConfig?.store_name || 'ONDC Seller'}
              </span>
            </Link>
          </div>

          {/* Search Bar */}
          <div className='flex-1 max-w-lg mx-8'>
            <form onSubmit={handleSearch} className='relative'>
              <input
                type='text'
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                placeholder='Search products, orders, customers...'
                className='store-input pl-10 pr-4 py-2 rounded-lg w-full transition-all duration-200 hover:shadow-store focus:shadow-store-md'
              />
              <div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
                <svg
                  className='h-5 w-5 text-store-text-light'
                  fill='none'
                  viewBox='0 0 24 24'
                  stroke='currentColor'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'
                  />
                </svg>
              </div>
            </form>
          </div>

          {/* Navigation */}
          <nav className='hidden md:flex items-center space-x-2'>
            {navigationItems.map(item => (
              <Link
                key={item.name}
                href={item.href}
                className='store-nav-link text-sm font-medium'
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* User Menu */}
          <div className='flex items-center space-x-2'>
            <button
              className='p-2 rounded-md text-store-text-light hover:text-store-primary hover:bg-store-surface transition-all duration-200 store-focus-ring'
              title='Notifications'
            >
              <svg
                className='h-6 w-6'
                fill='none'
                viewBox='0 0 24 24'
                stroke='currentColor'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M15 17h5l-5 5-5-5h5v-12h5v12z'
                />
              </svg>
            </button>
            <button
              className='p-2 rounded-md text-store-text-light hover:text-store-primary hover:bg-store-surface transition-all duration-200 store-focus-ring'
              title='User Profile'
            >
              <svg
                className='h-6 w-6'
                fill='none'
                viewBox='0 0 24 24'
                stroke='currentColor'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z'
                />
              </svg>
            </button>
          </div>

          {/* Mobile menu button */}
          <div className='md:hidden'>
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className='p-2 rounded-md transition-colors hover:opacity-80'
              style={{
                color: 'var(--store-text-light)',
                backgroundColor: isMenuOpen
                  ? 'var(--store-surface)'
                  : 'transparent',
              }}
            >
              <svg
                className='h-6 w-6'
                fill='none'
                viewBox='0 0 24 24'
                stroke='currentColor'
              >
                {isMenuOpen ? (
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M6 18L18 6M6 6l12 12'
                  />
                ) : (
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M4 6h16M4 12h16M4 18h16'
                  />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile menu */}
        {isMenuOpen && (
          <div
            className='md:hidden'
            style={{ backgroundColor: 'var(--store-surface)' }}
          >
            <div className='px-2 pt-2 pb-3 space-y-1 sm:px-3'>
              {navigationItems.map(item => (
                <Link
                  key={item.name}
                  href={item.href}
                  className='block px-3 py-2 rounded-md text-base font-medium transition-colors hover:opacity-80'
                  style={{ color: 'var(--store-text)' }}
                  onMouseEnter={e => {
                    e.currentTarget.style.color = 'var(--store-primary)';
                  }}
                  onMouseLeave={e => {
                    e.currentTarget.style.color = 'var(--store-text)';
                  }}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
