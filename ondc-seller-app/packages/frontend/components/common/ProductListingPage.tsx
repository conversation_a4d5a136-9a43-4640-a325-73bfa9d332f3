'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import {
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  ShoppingCartIcon,
  EyeIcon,
} from '@heroicons/react/24/outline';
import { useCart } from '@/contexts/CartContext';
import { useHydratedCartStore } from '@/stores/cartStore';

import { formatCurrency } from '@/utils/formatCurrency';
import { useToast } from './ToastProvider';

export interface Product {
  id: number | string; // Allow both number and string IDs
  name: string;
  slug: string;
  originalPrice: number;
  salePrice: number;
  discount?: number;
  image: string;
  category: string;
  subcategory?: string;
  tenantId?: string;
  rating: number;
  reviewCount: number;
  badge?: string;
  brand?: string;
  inStock?: boolean;
  tags?: string[];
}

export interface ProductListingConfig {
  title: string;
  subtitle?: string;
  description?: string;
  showDiscountBadge?: boolean;
  showSaleBadge?: boolean;
  showPriceComparison?: boolean;
  showSavingsAmount?: boolean;
  buttonText?: string;
  buttonStyle?: string;
  headerImage?: string;
  breadcrumbs?: Array<{ label: string; href?: string }>;
  subcategoryFilters?: Array<{ id: string; name: string; count?: number }>;
  activeSubcategory?: string;
  showSubcategoryFilter?: boolean;
  showFilters?: boolean;
  showSort?: boolean;
  showViewToggle?: boolean;
  productUrlPattern?: 'deals' | 'products' | 'trending'; // Add URL pattern configuration
  storeHandle?: string;
}

interface ProductListingPageProps {
  products: Product[];
  config: ProductListingConfig;
  isLoading?: boolean;
  onSubcategoryChange?: (subcategoryId: string) => void;
  setProductId: (productId: string) => void;
  handleViewProduct: any;
}

export default function ProductListingPage({
  products,
  config,
  isLoading = false,
  onSubcategoryChange,
  setProductId,
  handleViewProduct,
}: ProductListingPageProps) {
  const toast = useToast();

  const [filteredProducts, setFilteredProducts] = useState<Product[]>(products);
  const [sortBy, setSortBy] = useState<string>('featured');
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 10000]);
  const [selectedBrands, setSelectedBrands] = useState<string[]>([]);
  const [minRating, setMinRating] = useState<number>(0);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [availabilityFilter, setAvailabilityFilter] = useState<
    'all' | 'in-stock' | 'out-of-stock'
  >('all');

  // Format price in INR
  // const formatCurrency = (price: number) => {
  //   return new Intl.NumberFormat('en-IN', {
  //     style: 'currency',
  //     currency: 'INR',
  //     minimumFractionDigits: 0,
  //     maximumFractionDigits: 0,
  //   }).format(price);
  // };

  // Get brands from current products
  const brands = Array.from(
    new Set(
      products.map(product => {
        if (product.brand) return product.brand;
        // Extract brand from product name or use a default mapping
        if (product.name.includes('Designer')) return 'Designer Collection';
        if (product.name.includes('Premium')) return 'Premium Brand';
        if (product.name.includes('Luxury')) return 'Luxury Line';
        if (product.name.includes('Professional')) return 'Professional Series';
        if (product.name.includes('Smart')) return 'Smart Tech';
        return 'Brand Collection';
      })
    )
  );

  useEffect(() => {
    setFilteredProducts(products);
    // Reset price range based on products
    // if (products.length > 0) {
    //   const prices = products.map(p => p.salePrice);
    //   const minPrice = Math.min(0);
    //   const maxPrice = Math.max(10000);
    //   setPriceRange([
    //     Math.floor(minPrice / 100) * 100,
    //     Math.ceil(maxPrice / 100) * 100,
    //   ]);
    // }
  }, [products]);

  useEffect(() => {
    // Filter and sort products
    let filtered = [...products];

    // Filter by price range
    filtered = filtered.filter(
      product =>
        product.salePrice >= priceRange[0] && product.salePrice <= priceRange[1]
    );

    // Filter by brands
    if (selectedBrands.length > 0) {
      filtered = filtered.filter(product => {
        const productBrand =
          product.brand ||
          (product.name.includes('Designer')
            ? 'Designer Collection'
            : product.name.includes('Premium')
              ? 'Premium Brand'
              : product.name.includes('Luxury')
                ? 'Luxury Line'
                : product.name.includes('Professional')
                  ? 'Professional Series'
                  : product.name.includes('Smart')
                    ? 'Smart Tech'
                    : 'Brand Collection');
        return selectedBrands.includes(productBrand);
      });
    }

    // Filter by rating
    if (minRating > 0) {
      filtered = filtered.filter(product => product.rating >= minRating);
    }

    // Filter by availability
    if (availabilityFilter === 'in-stock') {
      filtered = filtered.filter(product => product.inStock !== false);
    } else if (availabilityFilter === 'out-of-stock') {
      filtered = filtered.filter(product => product.inStock === false);
    }

    // Sort products
    switch (sortBy) {
      case 'featured':
        // Keep original order for featured
        break;
      case 'price-low':
        filtered.sort((a, b) => a.salePrice - b.salePrice);
        break;
      case 'price-high':
        filtered.sort((a, b) => b.salePrice - a.salePrice);
        break;
      case 'rating':
        filtered.sort((a, b) => b.rating - a.rating);
        break;
      case 'newest':
        filtered.sort((a, b) => b.id - a.id);
        break;
      case 'best-sellers':
        filtered.sort((a, b) => b.reviewCount - a.reviewCount);
        break;
      default:
        break;
    }
    console.log('filtered:::::::::::', filtered);
    setFilteredProducts(filtered);
  }, [
    products,
    sortBy,
    priceRange,
    selectedBrands,
    minRating,
    availabilityFilter,
  ]);

  const clearFilters = () => {
    setPriceRange([0, 10000]);
    setSelectedBrands([]);
    setMinRating(0);
    setAvailabilityFilter('all');
  };

  const hasActiveFilters =
    selectedBrands.length > 0 ||
    minRating > 0 ||
    availabilityFilter !== 'all' ||
    priceRange[0] > 0 ||
    priceRange[1] < 10000;
  console.log('config::::::::', config);
  return (
    <div className='min-h-screen bg-gray-50'>
      {/* Header Section */}
      <div className='bg-white border-b border-gray-200'>
        <div className='px-4 sm:px-6 lg:px-8 py-6'>
          {/* Header Image and Title */}
          <div className='flex items-center justify-between mb-4'>
            <div className='flex items-center space-x-4'>
              {config.headerImage && (
                <div className='w-16 h-16 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0'>
                  <Image
                    src={config.headerImage}
                    alt={config.title}
                    width={64}
                    height={64}
                    className='w-full h-full object-cover'
                  />
                </div>
              )}
              <div>
                <h1 className='text-2xl font-bold text-gray-900'>
                  {config.title}
                </h1>
                <p className='text-sm text-gray-600 mt-1'>{config.subtitle}</p>
                <p className='text-sm text-gray-500 mt-1'>
                  {filteredProducts.length} products available
                </p>
              </div>
            </div>
          </div>

          {/* Breadcrumbs */}
          {config.breadcrumbs && config.breadcrumbs.length > 0 && (
            <nav className='flex items-center space-x-2 text-sm text-gray-500 mb-4'>
              {config.breadcrumbs.map((crumb, index) => (
                <React.Fragment key={index}>
                  {index > 0 && (
                    <svg
                      className='w-4 h-4 text-gray-400'
                      fill='currentColor'
                      viewBox='0 0 20 20'
                    >
                      <path
                        fillRule='evenodd'
                        d='M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z'
                        clipRule='evenodd'
                      />
                    </svg>
                  )}
                  {crumb.href ? (
                    <Link
                      href={crumb.href}
                      className='hover:text-blue-600 transition-colors'
                    >
                      {crumb.label}
                    </Link>
                  ) : (
                    <span className='text-gray-900 font-medium'>
                      {crumb.label}
                    </span>
                  )}
                </React.Fragment>
              ))}
            </nav>
          )}

          {/* Subcategory Filters */}
          {config.showSubcategoryFilter !== false &&
            config.subcategoryFilters &&
            config.subcategoryFilters.length > 0 && (
              <div className='mb-6'>
                <div className='flex flex-wrap gap-3'>
                  <button
                    onClick={() => onSubcategoryChange?.('all')}
                    className={`px-4 py-2 rounded-full text-sm font-medium transition-colors border ${
                      !config.activeSubcategory ||
                      config.activeSubcategory === 'all'
                        ? 'bg-blue-600 text-white border-blue-600'
                        : 'bg-white text-gray-700 border-gray-300 hover:border-blue-600 hover:text-blue-600'
                    }`}
                  >
                    All ({products.length})
                  </button>
                  {config.subcategoryFilters
                    .filter(filter => filter.id !== 'all') // Exclude 'all' since we handle it separately
                    .map(filter => (
                      <button
                        key={filter.id}
                        onClick={() => onSubcategoryChange?.(filter.id)}
                        className={`px-4 py-2 rounded-full text-sm font-medium transition-colors border ${
                          config.activeSubcategory === filter.id
                            ? 'bg-blue-600 text-white border-blue-600'
                            : 'bg-white text-gray-700 border-gray-300 hover:border-blue-600 hover:text-blue-600'
                        }`}
                      >
                        {filter.name}{' '}
                        {filter.count !== undefined && `(${filter.count})`}
                      </button>
                    ))}
                </div>
              </div>
            )}
        </div>
      </div>

      {/* Main Content */}
      <div className='px-4 sm:px-6 lg:px-8 py-6'>
        <div className='flex flex-col lg:flex-row gap-6'>
          {/* Mobile Filter Toggle */}
          <div className='lg:hidden'>
            <button
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
              className='flex items-center justify-center w-full bg-white border border-gray-300 rounded-lg px-4 py-3 text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors'
            >
              <FunnelIcon className='h-4 w-4 mr-2' />
              Filters
              {isSidebarOpen ? (
                <span className='ml-2'>✕</span>
              ) : (
                <span className='ml-2'>⚙️</span>
              )}
            </button>
          </div>

          {/* Sidebar Filters */}
          <div
            className={`lg:w-80 ${isSidebarOpen ? 'block' : 'hidden lg:block'}`}
          >
            <div className='bg-white rounded-lg border border-gray-200 shadow-sm p-6 sticky top-6'>
              <div className='flex items-center justify-between mb-6'>
                <h3 className='text-lg font-semibold text-gray-900 flex items-center'>
                  <FunnelIcon className='h-5 w-5 mr-2 text-gray-600' />
                  Filters
                </h3>
                {hasActiveFilters && (
                  <button
                    onClick={clearFilters}
                    className='text-sm text-blue-600 hover:text-blue-700 font-medium'
                  >
                    Clear
                  </button>
                )}
              </div>

              {hasActiveFilters ? (
                <div className='mb-4 text-sm text-blue-600 bg-blue-50 px-3 py-2 rounded-lg'>
                  Filters applied
                </div>
              ) : (
                <div className='mb-4 text-sm text-gray-500'>
                  No filters applied
                </div>
              )}

              {/* Price Range Filter */}
              <div className='mb-6'>
                <label className='block text-sm font-medium text-gray-700 mb-3'>
                  Price Range: {formatCurrency(priceRange[0])} -{' '}
                  {formatCurrency(priceRange[1])}
                </label>
                <div className='flex items-center space-x-3 mb-4'>
                  <div className='flex-1'>
                    <label className='block text-xs text-gray-500 mb-1'>
                      Min Price (₹)
                    </label>
                    <input
                      type='number'
                      min='0'
                      max='10000'
                      step='100'
                      value={priceRange[0]}
                      onChange={e => {
                        const minValue = Math.max(
                          0,
                          parseInt(e.target.value) || 0
                        );
                        const maxValue = Math.max(minValue, priceRange[1]);
                        setPriceRange([minValue, maxValue]);
                      }}
                      className='w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
                      placeholder='0'
                    />
                  </div>
                  <div className='text-gray-400 pt-5'>-</div>
                  <div className='flex-1'>
                    <label className='block text-xs text-gray-500 mb-1'>
                      Max Price (₹)
                    </label>
                    <input
                      type='number'
                      min='0'
                      max='10000'
                      step='100'
                      value={priceRange[1]}
                      onChange={e => {
                        const maxValue = Math.max(
                          0,
                          parseInt(e.target.value) || 10000
                        );
                        const minValue = Math.min(maxValue, priceRange[0]);
                        setPriceRange([minValue, maxValue]);
                      }}
                      className='w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
                      placeholder='10000'
                    />
                  </div>
                </div>
              </div>

              {/* Brand Filter */}
              {brands.length > 1 && (
                <div className='mb-6'>
                  <label className='block text-sm font-medium text-gray-700 mb-3'>
                    Brands
                  </label>
                  <div className='space-y-2 max-h-40 overflow-y-auto'>
                    {brands.map(brand => (
                      <label key={brand} className='flex items-center'>
                        <input
                          type='checkbox'
                          checked={selectedBrands.includes(brand)}
                          onChange={e => {
                            if (e.target.checked) {
                              setSelectedBrands([...selectedBrands, brand]);
                            } else {
                              setSelectedBrands(
                                selectedBrands.filter(b => b !== brand)
                              );
                            }
                          }}
                          className='h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded'
                        />
                        <span className='ml-2 text-sm text-gray-700'>
                          {brand}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>
              )}

              {/* Rating Filter */}
              <div className='mb-6'>
                <label className='block text-sm font-medium text-gray-700 mb-3'>
                  Rating
                </label>
                <div className='space-y-2'>
                  {[4, 3, 2, 1, 0].map(rating => (
                    <label key={rating} className='flex items-center'>
                      <input
                        type='radio'
                        name='rating'
                        value={rating}
                        checked={minRating === rating}
                        onChange={e => setMinRating(parseInt(e.target.value))}
                        className='h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300'
                      />
                      <span className='ml-2 flex items-center'>
                        {rating > 0 ? (
                          <>
                            {[...Array(5)].map((_, i) => (
                              <span
                                key={i}
                                className={`text-sm ${
                                  i < rating
                                    ? 'text-yellow-400'
                                    : 'text-gray-300'
                                }`}
                              >
                                ★
                              </span>
                            ))}
                            <span className='ml-1 text-sm text-gray-600'>
                              & up
                            </span>
                          </>
                        ) : (
                          <span className='text-sm text-gray-600'>
                            All Ratings
                          </span>
                        )}
                      </span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Availability Filter */}
              <div className='mb-6'>
                <label className='block text-sm font-medium text-gray-700 mb-3'>
                  Availability
                </label>
                <div className='space-y-2'>
                  {[
                    { value: 'all', label: 'All Products' },
                    { value: 'in-stock', label: 'In Stock' },
                    { value: 'out-of-stock', label: 'Out of Stock' },
                  ].map(option => (
                    <label key={option.value} className='flex items-center'>
                      <input
                        type='radio'
                        name='availability'
                        value={option.value}
                        checked={availabilityFilter === option.value}
                        onChange={e =>
                          setAvailabilityFilter(e.target.value as any)
                        }
                        className='h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300'
                      />
                      <span className='ml-2 text-sm text-gray-700'>
                        {option.label}
                      </span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Clear Filters Button */}
              <button
                onClick={clearFilters}
                disabled={!hasActiveFilters}
                className={`w-full py-2 px-4 rounded-lg transition-colors font-medium ${
                  hasActiveFilters
                    ? 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                    : 'bg-gray-50 text-gray-400 cursor-not-allowed'
                }`}
              >
                Clear All Filters
              </button>
            </div>
          </div>

          {console.log('filteredProducts::::::::::::::', filteredProducts)}
          {/* Products Grid */}
          <div className='flex-1'>
            {/* Results Header with Sort and View Toggle */}
            <div className='flex items-center justify-between mb-6'>
              <div>
                <h2 className='text-xl font-semibold text-gray-900'>
                  {filteredProducts.length} Product
                  {filteredProducts.length !== 1 ? 's' : ''} Found
                </h2>
                <p className='text-sm text-gray-600 mt-1'>
                  Showing {filteredProducts.length} of {products.length} total
                  products
                </p>
              </div>

              <div className='flex items-center space-x-4'>
                {/* View Mode Toggle */}
                <div className='flex items-center bg-gray-100 rounded-lg p-1'>
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 rounded-md transition-colors ${
                      viewMode === 'grid'
                        ? 'bg-white text-gray-900 shadow-sm'
                        : 'text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    <Squares2X2Icon className='h-4 w-4' />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 rounded-md transition-colors ${
                      viewMode === 'list'
                        ? 'bg-white text-gray-900 shadow-sm'
                        : 'text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    <ListBulletIcon className='h-4 w-4' />
                  </button>
                </div>

                {/* Sort By */}
                <div className='flex items-center space-x-2'>
                  <span className='text-sm font-medium text-gray-700'>
                    Sort by:
                  </span>
                  <select
                    value={sortBy}
                    onChange={e => setSortBy(e.target.value)}
                    className='border border-gray-300 rounded-lg px-3 py-2 text-sm bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-w-[160px]'
                  >
                    <option value='featured'>Featured</option>
                    <option value='price-low'>Price: Low to High</option>
                    <option value='price-high'>Price: High to Low</option>
                    <option value='rating'>Highest Rated</option>
                    <option value='newest'>Newest First</option>
                    <option value='best-sellers'>Best Sellers</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Products Grid/List */}
            {isLoading ? (
              <div
                className={`grid gap-6 ${
                  viewMode === 'grid'
                    ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'
                    : 'grid-cols-1'
                }`}
              >
                {[...Array(6)].map((_, index) => (
                  <div
                    key={index}
                    className={`bg-white rounded-xl border border-gray-200 animate-pulse ${
                      viewMode === 'grid'
                        ? 'h-[480px] flex flex-col'
                        : 'h-[200px] flex p-6'
                    }`}
                  >
                    {viewMode === 'grid' ? (
                      <>
                        <div className='h-48 bg-gray-200 rounded-lg mb-4'></div>
                        <div className='p-4 flex-1 flex flex-col'>
                          <div className='h-3 bg-gray-200 rounded mb-2 w-1/3'></div>
                          <div className='h-4 bg-gray-200 rounded mb-2'></div>
                          <div className='h-4 bg-gray-200 rounded w-2/3 mb-3'></div>
                          <div className='h-3 bg-gray-200 rounded mb-2 w-1/2'></div>
                          <div className='h-6 bg-gray-200 rounded mb-4 w-1/3'></div>
                          <div className='mt-auto h-10 bg-gray-200 rounded'></div>
                        </div>
                      </>
                    ) : (
                      <>
                        <div className='w-48 h-full bg-gray-200 rounded-lg mr-4'></div>
                        <div className='flex-1'>
                          <div className='h-4 bg-gray-200 rounded mb-2'></div>
                          <div className='h-4 bg-gray-200 rounded w-2/3 mb-3'></div>
                          <div className='h-3 bg-gray-200 rounded mb-2'></div>
                          <div className='h-10 bg-gray-200 rounded'></div>
                        </div>
                      </>
                    )}
                  </div>
                ))}
              </div>
            ) : filteredProducts.length === 0 ? (
              <div className='text-center py-12'>
                <div className='text-gray-400 mb-4'>
                  <svg
                    className='mx-auto h-12 w-12'
                    fill='none'
                    viewBox='0 0 24 24'
                    stroke='currentColor'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5'
                    />
                  </svg>
                </div>
                <h3 className='text-lg font-medium text-gray-900 mb-2'>
                  No products found
                </h3>
                <p className='text-gray-500 mb-4'>
                  Try adjusting your filters or search criteria
                </p>
                {hasActiveFilters && (
                  <button
                    onClick={clearFilters}
                    className='bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors'
                  >
                    Clear Filters
                  </button>
                )}
              </div>
            ) : (
              <div
                className={`grid gap-6 ${
                  viewMode === 'grid'
                    ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'
                    : 'grid-cols-1'
                }`}
              >
                {filteredProducts.map(product => (
                  <ProductCard
                    key={product.id}
                    product={product}
                    config={config}
                    viewMode={viewMode}
                    formatCurrency={formatCurrency}
                    setProductId={setProductId}
                    handleViewProduct={handleViewProduct}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

// Product Card Component
interface ProductCardProps {
  product: Product;
  config: ProductListingConfig;
  viewMode: 'grid' | 'list';
  formatCurrency: (price: number) => string;
  setProductId: (productId: string) => string;
  handleViewProduct?: any;
}

function ProductCard({
  product,
  config,
  viewMode,
  formatCurrency,
  setProductId,
  handleViewProduct,
}: ProductCardProps) {
  const { addItem } = useCart();
  const { addItem: addToZustandCart } = useHydratedCartStore();
  const [isLoading, setIsLoading] = useState(false);

  // Generate product URL based on the page context
  const getProductUrl = () => {
    switch (config.productUrlPattern) {
      case 'products':
        console.log('inside product');

        return `/${config.storeHandle}/products/${product.id}`;
      case 'trending':
        console.log('inside trending');

        return `/product/trending/general?product-name=${product.slug}`;
      case 'deals':
      default:
        return `/${config.storeHandle}/products/${product.id}`;
      // return `/product/${product.category.toLowerCase().replace(/\s+/g, '-')}/${product.subcategory?.toLowerCase().replace(/\s+/g, '-') || 'general'}?product-name=${product.slug}`;
    }
  };

  const productUrl = getProductUrl();

  const handleAddToCart = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    setIsLoading(true);

    try {
      const cartItem = {
        id: `${product.id}-default`,
        productId: product.id.toString(),
        name: product.name,
        price: product.salePrice,
        quantity: 1,
        image: `https://picsum.photos/400/400?random=${product.slug}-${product.id}`,
        variant: {
          id: 'default',
          name: 'Default',
          sku: `${product.slug.toUpperCase()}-001`,
        },
        maxQuantity: 50,
        sellerId: 'ondc-seller',
        sellerName: product.brand || 'Brand Collection',
      };

      // Add to both cart systems for compatibility
      addItem(cartItem, 1);
      addToZustandCart(cartItem, 1);

      // Show success toast
      toast.success(`Added ${product.name} to cart!`);
    } catch (error) {
      console.error('Failed to add to cart:', error);

      // Show error toast
      toast.error('Failed to add item to cart. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (viewMode === 'list') {
    return (
      <div className='bg-white rounded-xl border border-gray-200 hover:border-gray-300 hover:shadow-lg transition-all duration-300 overflow-hidden group'>
        <div className='flex p-6'>
          {/* Product Image */}
          <div className='relative w-48 h-48 overflow-hidden bg-gray-50 flex-shrink-0 rounded-lg'>
            <Link href={productUrl} onClick={() => setProductId(product.id)}>
              <Image
                src={`https://picsum.photos/400/400?random=${product.slug}-${product.id}`}
                alt={product.name}
                fill
                className='object-cover transition-transform duration-300 group-hover:scale-105'
                sizes='192px'
              />
            </Link>

            {/* Badges */}
            <div className='absolute top-3 left-3 flex flex-col space-y-2'>
              {config.showDiscountBadge &&
                product.discount &&
                product.discount > 0 && (
                  <div className='bg-gradient-to-r from-red-500 to-red-600 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg'>
                    -{product.discount}%
                  </div>
                )}
              {config.showSaleBadge && product.badge && (
                <div className='bg-gradient-to-r from-orange-500 to-orange-600 text-white text-xs font-semibold px-2 py-1 rounded-full shadow-lg'>
                  {product.badge}
                </div>
              )}
            </div>
          </div>

          {/* Product Info */}
          <div className='flex-1 ml-6 flex flex-col'>
            {/* Category */}
            <div className='text-sm text-gray-500 mb-2 uppercase tracking-wide font-medium'>
              {product.category}
            </div>

            {/* Product Name */}
            <Link href={productUrl} onClick={() => setProductId(product.id)}>
              <h3 className='font-semibold text-gray-900 hover:text-blue-600 transition-colors text-lg leading-tight mb-3'>
                {product.name}
              </h3>
            </Link>

            {/* Rating */}
            <div className='flex items-center mb-4'>
              <div className='flex items-center'>
                {[...Array(5)].map((_, i) => (
                  <span
                    key={i}
                    className={`text-base ${
                      i < Math.floor(product.rating)
                        ? 'text-yellow-400'
                        : 'text-gray-300'
                    }`}
                  >
                    ★
                  </span>
                ))}
              </div>
              <span className='text-sm text-gray-500 ml-2'>
                ({product.reviewCount})
              </span>
            </div>

            {/* Prices */}
            <div className='mb-4 flex-grow'>
              <div className='flex items-baseline space-x-2 mb-2'>
                <span className='text-xl font-bold text-gray-900'>
                  {formatCurrency(product.salePrice)}
                </span>
                {config.showPriceComparison &&
                  product.originalPrice > product.salePrice && (
                    <span className='text-base text-gray-500 line-through'>
                      {formatCurrency(product.originalPrice)}
                    </span>
                  )}
              </div>
              {config.showSavingsAmount &&
                product.originalPrice > product.salePrice && (
                  <span className='text-sm text-green-600 font-medium bg-green-50 px-2 py-1 rounded-full'>
                    Save{' '}
                    {formatCurrency(product.originalPrice - product.salePrice)}
                  </span>
                )}
            </div>

            {/* Stock Status */}
            {product.inStock === false && (
              <div className='text-sm text-red-600 font-medium mb-3'>
                Out of Stock
              </div>
            )}

            {/* Action Buttons */}
            <div className='flex items-center gap-2'>
              <Link
                href={productUrl}
                className='p-2 text-gray-600 hover:text-blue-600 transition-colors'
                onClick={() => {
                  console.log('data::::::', { product });
                  setProductId(product.id);
                }}
              >
                <EyeIcon className='h-5 w-5' />
              </Link>
              <button
                // onClick={handleAddToCart}

                disabled={isLoading || product.inStock === false}
                className={`flex items-center gap-2 py-3 px-6 rounded-lg transition-all duration-300 font-semibold text-sm ${
                  config.buttonStyle ||
                  'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-md hover:shadow-lg'
                } ${
                  product.inStock === false
                    ? 'opacity-50 cursor-not-allowed'
                    : ''
                }`}
              >
                {isLoading ? (
                  <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white'></div>
                ) : (
                  <ShoppingCartIcon className='h-4 w-4' />
                )}
                {config.buttonText || 'Add to Cart'}
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Grid View
  return (
    <div className='bg-white rounded-xl border border-gray-200 hover:border-gray-300 hover:shadow-xl transition-all duration-300 overflow-hidden group h-[480px] flex flex-col'>
      {/* Product Image */}
      <div className='relative h-48 overflow-hidden bg-gray-50 flex-shrink-0'>
        <Link href={productUrl}>
          <Image
            src={`https://picsum.photos/400/400?random=${product.slug}-${product.id}`}
            alt={product.name}
            fill
            className='object-cover transition-transform duration-300 group-hover:scale-105'
            sizes='(max-width: 768px) 50vw, (max-width: 1024px) 33vw, 33vw'
          />
        </Link>

        {/* Badges */}
        <div className='absolute top-3 left-3 flex flex-col space-y-2'>
          {config.showDiscountBadge &&
            product.discount &&
            product.discount > 0 && (
              <div className='bg-gradient-to-r from-red-500 to-red-600 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg'>
                -{product.discount}%
              </div>
            )}
        </div>

        {config.showSaleBadge && product.badge && (
          <div className='absolute top-3 right-3 bg-gradient-to-r from-orange-500 to-orange-600 text-white text-xs font-semibold px-2 py-1 rounded-full shadow-lg'>
            {product.badge}
          </div>
        )}
      </div>

      {/* Product Info */}
      <div className='p-4 flex flex-col flex-grow'>
        {/* Category */}
        <div className='text-xs text-gray-500 mb-2 uppercase tracking-wide font-medium'>
          {product.category}
        </div>
        {console.log(
          '/${config?.slug}/${product.slug}::::::',
          `/${config?.slug}/${product.slug}`
        )}

        {/* Product Name */}
        <Link
          href={config?.slug ? `${config?.slug}/${product.slug}` : productUrl}
          // onClick={handleViewProduct}
          onClick={() => handleViewProduct(product.id)}
        >
          <h3 className='font-semibold text-gray-900 hover:text-blue-600 transition-colors line-clamp-2 mb-3 text-base leading-tight min-h-[2.5rem]'>
            {product.name}
          </h3>
        </Link>

        {/* Rating */}
        <div className='flex items-center mb-3'>
          <div className='flex items-center'>
            {[...Array(5)].map((_, i) => (
              <span
                key={i}
                className={`text-sm ${
                  i < Math.floor(product.rating)
                    ? 'text-yellow-400'
                    : 'text-gray-300'
                }`}
              >
                ★
              </span>
            ))}
          </div>
          <span className='text-xs text-gray-500 ml-2'>
            ({product.reviewCount})
          </span>
        </div>
        {console.log({ product, config })}
        {/* Prices */}
        <div className='mb-3'>
          <div className='flex items-baseline space-x-2 mb-2'>
            <span className='text-xl font-bold text-gray-900'>
              {formatCurrency(product.salePrice)}
            </span>
            {config.showPriceComparison &&
              product.originalPrice > product.salePrice && (
                <span className='text-sm text-gray-500 line-through'>
                  {formatCurrency(product.originalPrice)}
                </span>
              )}
          </div>
          {config.showSavingsAmount &&
            product.originalPrice > product.salePrice && (
              <span className='text-xs text-green-600 font-medium bg-green-50 px-2 py-1 rounded-full'>
                Save {formatCurrency(product.originalPrice - product.salePrice)}
              </span>
            )}
        </div>

        {/* Stock Status */}
        {product.inStock === false && (
          <div className='text-xs text-red-600 font-medium mb-2'>
            Out of Stock
          </div>
        )}

        {/* Action Button */}
        <div className='mt-auto pt-2'>
          <Link
            href={config?.slug ? `${config?.slug}/${product.slug}` : productUrl}
          >
            <button
              onClick={() => handleViewProduct(product.id)}
              disabled={isLoading || product.inStock === false}
              className='w-full py-2.5 px-6 rounded-lg transition-all duration-300 font-semibold text-sm bg-blue-600 hover:bg-blue-700 text-white shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2'
            >
              {isLoading ? (
                <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white'></div>
              ) : (
                <ShoppingCartIcon className='h-4 w-4' />
              )}
              {config.buttonText || 'View Product'}
            </button>
          </Link>
        </div>
      </div>
    </div>
  );
}
