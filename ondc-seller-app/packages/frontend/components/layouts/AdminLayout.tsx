'use client';

import React, { useState, useEffect, useMemo } from 'react';
import Link from 'next/link';
import { usePathname, useRouter, useParams } from 'next/navigation';
import { createPortal } from 'react-dom';

// import { useAuth } from '@/context/AuthContext';
import { useAuth } from '@/contexts/AuthContext';
import { useTenant } from '@/contexts/TenantContext';
import { useRBAC } from '@/hooks/useRBAC';
import { getStoreName } from '@/lib/api/multi-tenant';
import { OptimizedLoadingProvider } from '@/contexts/OptimizedLoadingContext';

import {
  OptimizedSidebarNavLink,
  OptimizedDropdownNavLink,
} from '@/components/admin/OptimizedAdminNavLink';

import ResponsiveContainer from '@/components/layouts/ResponsiveContainer';
import {
  HomeIcon,
  ShoppingBagIcon,
  TagIcon,
  RectangleStackIcon,
  ClipboardDocumentListIcon,
  UsersIcon,
  TicketIcon,
  Bars3Icon,
  XMarkIcon,
  BellIcon,
  ArrowRightOnRectangleIcon,
  ChevronDownIcon,
  ChartBarIcon,
  ShoppingCartIcon,
  CloudArrowUpIcon,
} from '@heroicons/react/24/outline';
import {
  LoadingBackdropProvider,
  useLoadingBackdrop,
} from '@/contexts/LoadingBackdropContext';
import { PerformanceIndicator } from '@/components/admin/PerformanceMonitor';
import { RoutePrefetchProvider } from '@/lib/route-prefetcher';
import { useAuthStore } from '@/stores/authStore';

interface AdminLayoutProps {
  children: React.ReactNode;
}

interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<any>;
  children?: NavItem[];
  onClick?: () => void;
}

// Function to generate navigation with store handle
const getNavigation = (storeHandle: string, storeName: string): NavItem[] => [
  {
    name: 'Quick links',
    href: '#',
    icon: HomeIcon,
    children: [
      { name: 'Dashboard', href: `/${storeHandle}/admin`, icon: HomeIcon },
    ],
  },
  {
    name: 'Catalog',
    href: '#',
    icon: ShoppingBagIcon,
    children: [
      {
        name: 'Products',
        href: `/${storeHandle}/admin/products`,
        icon: ShoppingBagIcon,
      },
      // {
      //   name: 'Bulk Upload',
      //   href: `/${storeHandle}/admin/products/bulk-upload`,
      //   icon: CloudArrowUpIcon,
      // },
      {
        name: 'Categories',
        href: `/${storeHandle}/admin/categories`,
        icon: TagIcon,
      },
      {
        name: 'Collections',
        href: `/${storeHandle}/admin/collections`,
        icon: RectangleStackIcon,
      },
      { name: 'Tags', href: `/${storeHandle}/admin/tags`, icon: TagIcon },
    ],
  },
  {
    name: 'Sale',
    href: '#',
    icon: ClipboardDocumentListIcon,
    children: [
      {
        name: 'Orders',
        href: `/${storeHandle}/admin/orders`,
        icon: ClipboardDocumentListIcon,
      },
    ],
  },
  {
    name: 'Customer',
    href: '#',
    icon: UsersIcon,
    children: [
      {
        name: 'Customers',
        href: `/${storeHandle}/admin/customers`,
        icon: UsersIcon,
      },
      // {
      //   name: 'Cart & Wishlist',
      //   href: `/${storeHandle}/admin/users/cart-wishlist`,
      //   icon: ShoppingCartIcon,
      // },
    ],
  },
  // {
  //   name: 'Analytics',
  //   href: '#',
  //   icon: ChartBarIcon,
  //   children: [
  //     {
  //       name: 'User Activity',
  //       href: `/${storeHandle}/admin/analytics/user-activity`,
  //       icon: ChartBarIcon,
  //     },
  //   ],
  // },
  {
    name: 'Promotion',
    href: '#',
    icon: TicketIcon,
    children: [
      {
        name: 'Coupons',
        href: `/${storeHandle}/admin/coupons`,
        icon: TicketIcon,
      },
    ],
  },
  {
    name: 'Store Setting',
    href: '#',
    icon: TicketIcon,
    children: [
      {
        name: 'Store setting',
        href: `/${storeHandle}/admin/store-setting`,
        icon: TicketIcon,
      },
    ],
  },
];

// Inner component that uses optimized loading context
const AdminLayoutContent = React.memo(function AdminLayoutContent({
  children,
}: AdminLayoutProps) {
  const [adminToken, setAdminToken] = useState<string | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [expandedSections, setExpandedSections] = useState<string[]>([
    'Quick links',
  ]);
  const pathname = usePathname();
  const router = useRouter();
  const params = useParams();
  const { logout, isAdmin, isLoading } = useAuth();
  const { selectedTenant } = useTenant();
  const { isAdmin: rbacIsAdmin, userRole } = useRBAC();
  const { user, token, clearStorage } = useAuthStore();
  const { showLoading, hideLoading } = useLoadingBackdrop();

  // Get store handle from URL params or user data
  const storeHandle =
    (params?.storeHandle as string) ||
    (user as any)?.store_handle ||
    `store-${user?.email?.split('@')[0] || 'default'}`;

  // Generate navigation with store handle
  // const navigation = useMemo(() => getNavigation(storeHandle,storeName), [
  //   storeHandle,
  // ]);
  console.log(':::::::::user::::::::::', user);
  const navigation = useMemo(
    () =>
      getNavigation(
        user?.store_handle || 'dev-store',
        user?.store_name || 'Development Store'
      ),
    [user]
  );

  // Use RBAC admin check as fallback
  const hasAdminAccess =
    isAdmin ||
    rbacIsAdmin ||
    userRole === 'admin' ||
    userRole === 'super_admin';
  console.log(':::::::hasAdminAccess::::::::', hasAdminAccess);
  // Debug logging removed for production

  // Get store name with fallback
  const storeName = getStoreName(selectedTenant);

  // Safely get admin token from localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('ondc_auth_token');
      setAdminToken(token);
    }
  }, []);

  // Redirect if not admin
  useEffect(() => {
    if (user && !hasAdminAccess) {
      console.log('🔍 Debug - Admin access check:', {
        user: user?.email,
        role: user?.role,
        userRole,
        isAdmin,
        rbacIsAdmin,
        hasAdminAccess,
      });
      // Redirect to user's store homepage instead of root
      const userStoreHandle =
        (user as any)?.store_handle ||
        `store-${user?.email?.split('@')[0] || 'dev'}`;
      router.push(`/${userStoreHandle}`);
    }
  }, [user, hasAdminAccess, router, userRole, isAdmin, rbacIsAdmin]);

  const toggleSection = React.useCallback((sectionName: string) => {
    setExpandedSections(prev =>
      prev.includes(sectionName)
        ? prev.filter(name => name !== sectionName)
        : [...prev, sectionName]
    );
  }, []);

  const toggleSidebar = React.useCallback(() => {
    setSidebarCollapsed(!sidebarCollapsed);
  }, [sidebarCollapsed]);

  const handleLogout = React.useCallback(() => {
    showLoading();
    // logout();
    clearStorage();
    router.push('/auth/login');
    hideLoading();
  }, [router]);

  // Show loading state while authentication is being checked
  // if (isLoading) {
  //   return (
  //     <div className='min-h-screen flex items-center justify-center bg-gray-50'>
  //       <div className='text-center'>
  //         <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4'></div>
  //         <h2 className='text-xl font-semibold text-gray-900 mb-2'>
  //           Loading...
  //         </h2>
  //         <p className='text-gray-600'>Checking authentication...</p>
  //       </div>
  //     </div>
  //   );
  // }

  console.log('🔍 Admin Access Debug:', {
    user: !!user,
    hasAdminAccess,
    adminToken: !!adminToken,
    userRole,
    isAdmin,
    rbacIsAdmin,
    condition: !user || !hasAdminAccess || !adminToken,
  });

  // 🔧 DEVELOPMENT: Allow access in development mode for testing
  const isDevelopment = process.env.NODE_ENV === 'development';

  // More lenient authentication check - only block if we're certain user is not authenticated
  // and not in development mode, and we've had time for auth to load
  const [authCheckComplete, setAuthCheckComplete] = useState(false);

  useEffect(() => {
    // Give time for authentication to load from storage
    const timer = setTimeout(() => {
      setAuthCheckComplete(true);
    }, 2000); // Wait 2 seconds for auth to fully load

    return () => clearTimeout(timer);
  }, []);

  // Only show access denied if:
  // 1. Auth check is complete (waited for loading)
  // 2. No token found
  // 3. Not in development mode
  // 4. User explicitly logged out (could add a flag for this)
  if (authCheckComplete && !token && !isDevelopment && !isLoading) {
    return (
      <div className='min-h-screen flex items-center justify-center bg-gray-50'>
        <div className='text-center'>
          <h2 className='text-2xl font-bold text-gray-900 mb-4'>
            Access Denied
          </h2>
          <p className='text-gray-600 mb-4'>
            You need admin privileges to access this area.
          </p>
          <Link
            href='/auth/login'
            className='inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700'
          >
            Login as Admin
          </Link>
        </div>
      </div>
    );
  }
  return (
    <div className='h-screen flex overflow-hidden bg-gray-100'>
      {/* Mobile sidebar */}
      <div
        className={`fixed inset-0 flex z-40 md:hidden ${
          sidebarOpen ? '' : 'hidden'
        }`}
      >
        <div
          className='fixed inset-0 bg-gray-600 bg-opacity-75'
          onClick={() => setSidebarOpen(false)}
        />
        <div className='relative flex-1 flex flex-col max-w-xs w-full bg-white'>
          <div className='absolute top-0 right-0 -mr-12 pt-2'>
            <button
              className='ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white'
              onClick={() => setSidebarOpen(false)}
            >
              <XMarkIcon className='h-6 w-6 text-white' />
            </button>
          </div>
          <SidebarContent
            navigation={navigation}
            pathname={pathname}
            expandedSections={expandedSections}
            toggleSection={toggleSection}
            storeName={user?.store_name || 'Development Store'}
            storeHandle={user?.store_handle || 'dev-store'}
          />
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className='hidden md:flex md:flex-shrink-0'>
        <div
          className={`flex flex-col h-screen transition-all duration-300 ${
            sidebarCollapsed ? 'w-16' : 'w-64'
          }`}
        >
          <SidebarContent
            navigation={navigation}
            pathname={pathname}
            expandedSections={expandedSections}
            toggleSection={toggleSection}
            collapsed={sidebarCollapsed}
            onToggleCollapse={toggleSidebar}
            storeName={user?.store_name || 'Development Store'}
            storeHandle={user?.store_handle || 'dev-store'}
          />
        </div>
      </div>

      {/* Main content */}
      <div className='flex flex-col w-0 flex-1 overflow-hidden'>
        {/* Top navigation */}
        <div className='relative z-10 flex-shrink-0 flex h-16 bg-white shadow'>
          <button
            className='px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 md:hidden'
            onClick={() => setSidebarOpen(true)}
          >
            <Bars3Icon className='h-6 w-6' />
          </button>

          <div className='flex-1 px-4 flex justify-end'>
            <div className='ml-4 flex items-center md:ml-6'>
              {/* Performance Indicator */}
              <PerformanceIndicator className='mr-3' />

              <button className='bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'>
                <BellIcon className='h-6 w-6' />
              </button>

              <div className='ml-3 relative'>
                <div className='flex items-center space-x-3'>
                  <div className='flex-shrink-0'>
                    <img
                      className='h-8 w-8 rounded-full'
                      src={user?.avatar || '/images/avatars/default.jpg'}
                      alt={`${user?.first_name || 'Dev'} ${
                        user?.last_name || 'User'
                      }`}
                    />
                  </div>
                  <div className='hidden md:block'>
                    <div className='text-sm font-medium text-gray-700'>
                      {`${user?.first_name || 'Dev'} ${
                        user?.last_name || 'User'
                      }`}
                    </div>
                    <div className='text-xs text-gray-500'>
                      {user?.role || 'admin'}
                    </div>
                  </div>
                  <button
                    onClick={handleLogout}
                    className='text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                  >
                    <ArrowRightOnRectangleIcon className='h-5 w-5' />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className='flex-1 relative overflow-y-auto focus:outline-none'>
          <ResponsiveContainer className='py-6' maxWidth='7xl'>
            {/* Breadcrumbs */}
            {/* <div className='mb-6'>
              <AdminBreadcrumbs />
            </div> */}

            {/* Page content */}
            <LoadingBackdropProvider>{children}</LoadingBackdropProvider>
          </ResponsiveContainer>
        </main>
      </div>
    </div>
  );
});

// Main AdminLayout component with OptimizedLoadingProvider and route prefetching
const AdminLayout = ({ children }: AdminLayoutProps) => {
  return (
    <OptimizedLoadingProvider>
      <RoutePrefetchProvider>
        <AdminLayoutContent>{children}</AdminLayoutContent>
      </RoutePrefetchProvider>
    </OptimizedLoadingProvider>
  );
};

export default AdminLayout;

interface SidebarContentProps {
  navigation: NavItem[];
  pathname: string | null;
  expandedSections: string[];
  toggleSection: (sectionName: string) => void;
  collapsed?: boolean;
  onToggleCollapse?: () => void;
  storeName: string;
  storeHandle: string;
}

const SidebarContent = React.memo(function SidebarContent({
  navigation,
  pathname: _pathname,
  expandedSections,
  toggleSection,
  collapsed = false,
  onToggleCollapse,
  storeName,
  storeHandle,
}: SidebarContentProps) {
  console.log('storeName::::::', storeName);
  const [hoveredItem, setHoveredItem] = React.useState<string | null>(null);
  const [hoverTimeout, setHoverTimeout] = React.useState<NodeJS.Timeout | null>(
    null
  );
  const [isPopoverVisible, setIsPopoverVisible] = React.useState(false);
  const [popoverPosition, setPopoverPosition] = React.useState<{
    top: number;
    left: number;
  } | null>(null);
  const [isMounted, setIsMounted] = React.useState(false);

  // Handle mouse enter with delay and position calculation
  const handleMouseEnter = React.useCallback(
    (itemName: string, event: React.MouseEvent) => {
      if (!collapsed) return;

      if (hoverTimeout) clearTimeout(hoverTimeout);

      // Calculate position immediately while event target is available
      const target = event.currentTarget as HTMLElement;
      if (!target) {
        console.warn('No target element found for hover');
        return;
      }

      try {
        const rect = target.getBoundingClientRect();
        const position = {
          top: rect.top,
          left: rect.right + 8, // 8px gap from sidebar
        };

        console.log('Hover detected:', itemName, 'Position:', position);

        const timeout = setTimeout(() => {
          setPopoverPosition(position);
          setHoveredItem(itemName);
          setIsPopoverVisible(true);
        }, 150); // Reduced delay for better responsiveness

        setHoverTimeout(timeout);
      } catch (error) {
        console.error('Error calculating popover position:', error);
      }
    },
    [collapsed, hoverTimeout]
  );

  // Handle mouse leave
  const handleMouseLeave = React.useCallback(() => {
    if (!collapsed) return;

    if (hoverTimeout) clearTimeout(hoverTimeout);

    // Add small delay before hiding to allow mouse movement to popover
    const timeout = setTimeout(() => {
      setHoveredItem(null);
      setIsPopoverVisible(false);
    }, 150); // Increased delay for better UX

    setHoverTimeout(timeout);
  }, [collapsed, hoverTimeout]);

  // Handle immediate hide (for clicks)
  const handleImmediateHide = React.useCallback(() => {
    if (hoverTimeout) clearTimeout(hoverTimeout);
    setHoveredItem(null);
    setIsPopoverVisible(false);
  }, [hoverTimeout]);

  // Mount effect
  React.useEffect(() => {
    setIsMounted(true);
  }, []);

  // Cleanup timeout on unmount
  React.useEffect(() => {
    return () => {
      if (hoverTimeout) clearTimeout(hoverTimeout);
    };
  }, [hoverTimeout]);
  return (
    <>
      <div className='flex flex-col h-full border-r border-gray-200 bg-white relative'>
        <div className='flex-1 flex flex-col pt-5 pb-4 overflow-y-auto'>
          <div className='flex items-center flex-shrink-0 px-4'>
            {collapsed ? (
              <button
                onClick={onToggleCollapse}
                className='w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center hover:bg-blue-700 transition-colors'
                title='Expand sidebar'
              >
                <Bars3Icon className='h-5 w-5 text-white' />
              </button>
            ) : (
              <div className='flex items-center space-x-3'>
                <button
                  onClick={onToggleCollapse}
                  className='p-1 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors'
                  title='Collapse sidebar'
                >
                  <Bars3Icon className='h-5 w-5' />
                </button>
                <Link
                  href={`/${storeHandle}/admin`}
                  className='text-xl font-bold text-blue-600'
                >
                  {storeName}
                </Link>
              </div>
            )}
          </div>

          <nav className='mt-5 flex-1 px-2 space-y-1 overflow-y-auto'>
            {navigation.map(item => (
              <div key={item.name} className='relative'>
                {item.children ? (
                  <div
                    onMouseEnter={e => handleMouseEnter(item.name, e)}
                    onMouseLeave={handleMouseLeave}
                    className='relative'
                  >
                    <button
                      onClick={() => !collapsed && toggleSection(item.name)}
                      className={`w-full group flex items-center px-2 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 ${
                        collapsed ? 'justify-center' : ''
                      } ${collapsed ? 'min-h-[44px]' : ''}`}
                      title={collapsed ? item.name : undefined}
                      data-menu-item={item.name}
                    >
                      <item.icon
                        className={`h-5 w-5 text-gray-400 group-hover:text-gray-500 ${
                          collapsed ? '' : 'mr-3'
                        }`}
                      />
                      {!collapsed && (
                        <>
                          {item.name}
                          <ChevronDownIcon
                            className={`ml-auto h-4 w-4 transform transition-transform ${
                              expandedSections.includes(item.name)
                                ? 'rotate-180'
                                : ''
                            }`}
                          />
                        </>
                      )}
                    </button>

                    {!collapsed && expandedSections.includes(item.name) && (
                      <div className='ml-6 space-y-1'>
                        {item.children.map(child => (
                          <OptimizedSidebarNavLink
                            key={child.name}
                            href={child.href}
                            icon={child.icon}
                          >
                            {child.name}
                          </OptimizedSidebarNavLink>
                        ))}
                      </div>
                    )}
                  </div>
                ) : (
                  <OptimizedSidebarNavLink
                    href={item.href}
                    icon={item.icon}
                    isCollapsed={collapsed}
                    onClick={item.onClick}
                  >
                    {item.name}
                  </OptimizedSidebarNavLink>
                )}
              </div>
            ))}
            {!collapsed && (
              // <div className='my-2 px-2 py-1 rounded-lg bg-gray-100 w-fit shadow-md'>
              <div>
                <Link
                  href={`/${storeHandle}`}
                  passHref
                  target='_blank'
                  // className='inline-block px-4 py-2 bg-blue-600 text-white rounded font-semibold transition-colors hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-400'
                  className='text-sm inline-block px-4 py-2 hover:font-semibold hover:underline'
                >
                  View - {storeName}
                </Link>
              </div>
            )}
          </nav>
        </div>

        <div className='flex-shrink-0 flex border-t border-gray-200 p-4'>
          {collapsed ? (
            <div className='w-full flex justify-center'>
              <div className='w-6 h-6 bg-blue-600 rounded flex items-center justify-center'>
                <span className='text-white font-bold text-xs'>E</span>
              </div>
            </div>
          ) : (
            <div className='text-xs text-gray-500'>
              &copy; {new Date().getFullYear()} {storeName}. All Rights
              Reserved.
              <br />
              Version 1.2.2
            </div>
          )}
        </div>
      </div>

      {/* Portal-based popover for collapsed sidebar */}
      {isMounted &&
        collapsed &&
        hoveredItem &&
        isPopoverVisible &&
        popoverPosition &&
        createPortal(
          <div
            className='fixed bg-white border border-gray-200 rounded-lg shadow-xl z-[9999] min-w-[200px] max-w-[280px] transition-all duration-200 ease-out'
            style={{
              top: popoverPosition.top,
              left: popoverPosition.left,
              opacity: isPopoverVisible ? 1 : 0,
              transform: isPopoverVisible
                ? 'translateX(0) scale(1)'
                : 'translateX(-8px) scale(0.95)',
            }}
            onMouseEnter={() => {
              if (hoverTimeout) clearTimeout(hoverTimeout);
              setIsPopoverVisible(true);
            }}
            onMouseLeave={handleMouseLeave}
            data-popover={hoveredItem}
          >
            <div className='py-2'>
              <div className='px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide border-b border-gray-100 bg-gray-50'>
                {hoveredItem}
              </div>
              <div className='py-1'>
                {navigation
                  .find(item => item.name === hoveredItem)
                  ?.children?.map(child => (
                    <OptimizedDropdownNavLink
                      key={child.name}
                      href={child.href}
                      icon={child.icon}
                      onClick={handleImmediateHide}
                    >
                      {child.name}
                    </OptimizedDropdownNavLink>
                  ))}
              </div>
            </div>
          </div>,
          document.body
        )}
    </>
  );
});
