'use client';

import React, { useState, useRef, useEffect, useMemo } from 'react';
import Link from 'next/link';
import { ChevronDownIcon, ArrowRightIcon } from '@heroicons/react/24/outline';
import { usecategories, Category } from '@/hooks/usecategories';
import { useCategoryStore } from '@/stores/categoriesStore';
import { useMedusaBackendCategories } from '@/hooks/useMedusaBackendProducts';

interface MegaMenuProps {
  className?: string;
  storeHandle: string;
}

interface SubCategory {
  id: string;
  name: string;
  slug: string;
  productCount: number;
  href: string;
}

interface MegaMenuCategory {
  id: string;
  name: string;
  slug: string;
  href: string;
  subcategories: SubCategory[];
}

// Helper function to convert Medusa category to MegaMenu format
const convertMedusaCategoryToMegaMenu = (
  category: Category,
  storeHandle: string
): MegaMenuCategory | null => {
  console.log('-----------cat', category);
  if (
    !Array.isArray(category?.subcategories) ||
    category.subcategories.length === 0
  ) {
    return null;
  }

  return {
    id: category.id,
    name: category.name,
    slug: category.slug,
    href: `/${storeHandle}/categories/${category.slug}`,
    subcategories: category.subcategories.map(sub => ({
      id: sub.id,
      name: sub.name,
      slug: sub.slug,
      productCount: sub.productCount || Math.floor(Math.random() * 200) + 50,
      href: `/${storeHandle}/categories/${category.slug}/${sub.slug}`,
    })),
  };
};

const MegaMenu: React.FC<MegaMenuProps> = ({
  className = '',
  storeHandle = '',
}) => {
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Store functions with null checks
  const setCategories = useCategoryStore(state => state.setCategories);
  const setSelectedCategoryId = useCategoryStore(
    state => state.setSelectedCategoryId
  );
  const setSelectedSubCategoryId = useCategoryStore(
    state => state.setSelectedSubCategoryId
  );

  // Get categories from Medusa backend
  const {
    filterCategories: categories,
    loading,
    error,
    fetchAllCategories,
  } = useMedusaBackendCategories();

  // Transform categories for mega menu
  const megaMenuData: MegaMenuCategory[] = useMemo(() => {
    console.log('========categories:::::::::', categories);

    if (setCategories) {
      setCategories(categories);
    }

    return categories
      .map(cat => convertMedusaCategoryToMegaMenu(cat, storeHandle))
      .filter((cat): cat is MegaMenuCategory => cat !== null);
  }, [categories, storeHandle]);
  console.log('========megaMenuData:::::::::', megaMenuData);

  const handleMouseEnter = (categoryId: string) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setActiveCategory(categoryId);
    setIsOpen(true);
  };

  const handleMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setActiveCategory(null);
      setIsOpen(false);
    }, 150);
  };

  const handleMenuMouseEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  };

  const handleMenuMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setActiveCategory(null);
      setIsOpen(false);
    }, 150);
  };

  useEffect(() => {
    if (fetchAllCategories) {
      fetchAllCategories();
    }
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [fetchAllCategories]);

  const activeCategoryData = megaMenuData.find(
    cat => cat?.id === activeCategory
  );

  // Loading state
  if (loading) {
    return (
      <div className={`${className}`}>
        <div className='flex items-center justify-center space-x-8 bg-store-background shadow-store'>
          {[1, 2, 3, 4, 5].map(index => (
            <div key={index} className='animate-pulse'>
              <div className='h-6 w-20 bg-store-surface rounded'></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error && categories.length === 0) {
    return (
      <div className={`${className}`}>
        <div className='flex items-center justify-center py-4 store-alert store-alert-error'>
          <div className='text-sm'>
            <span>Failed to load categories.</span>
            <button
              onClick={() => window.location.reload()}
              className='ml-2 underline hover:no-underline store-focus-ring rounded px-1'
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`} ref={menuRef}>
      {/* Main Navigation - Simple Names Only */}
      <nav className='bg-store-background shadow-store border-b border-store-border'>
        <div className='max-w-7xl mx-auto px-4'>
          <div className='flex items-center justify-center space-x-8 py-2'>
            {megaMenuData.map(category => (
              <div
                key={category?.id}
                className='relative'
                onMouseEnter={() => handleMouseEnter(category?.id)}
                onMouseLeave={handleMouseLeave}
              >
                <button
                  type='button'
                  className={`flex items-center space-x-1 px-3 py-2 text-sm font-medium transition-all duration-200 rounded-md store-focus-ring ${
                    activeCategory === category?.id
                      ? 'text-store-primary bg-store-secondary shadow-store'
                      : 'text-store-text hover:text-store-primary hover:bg-store-surface'
                  }`}
                  aria-expanded={activeCategory === category?.id}
                  aria-haspopup='true'
                >
                  <span>{category?.name}</span>
                  <ChevronDownIcon
                    className={`h-4 w-4 transition-transform duration-200 ${
                      activeCategory === category?.id ? 'rotate-180' : ''
                    }`}
                  />
                </button>
              </div>
            ))}
          </div>
        </div>
      </nav>

      {/* Submenu Dropdown - Fixed Width Grid */}
      {isOpen && activeCategoryData && (
        <div
          className='absolute left-0 right-0 top-full bg-store-background border-t border-store-border shadow-store-lg z-50'
          onMouseEnter={handleMenuMouseEnter}
          onMouseLeave={handleMenuMouseLeave}
        >
          <div className='max-w-7xl mx-auto p-6'>
            {/* Category Header */}
            <div className='flex items-center justify-between mb-4 pb-4 border-b border-store-border-light'>
              <h3 className='text-lg font-semibold text-store-text'>
                {activeCategoryData.name}
              </h3>
              <Link
                href={activeCategoryData.href}
                className='text-sm text-store-primary hover:text-store-primary-dark font-medium store-focus-ring rounded px-2 py-1 transition-colors duration-200'
                onClick={() => setSelectedCategoryId(activeCategoryData.id)}
              >
                View All
              </Link>
            </div>

            {/* Subcategories Grid - Fixed Width Columns */}
            <div className=''>
              {activeCategoryData.subcategories.map(subcategory => (
                <Link
                  key={subcategory.id}
                  href={subcategory.href}
                  className='group block p-3 border-b border-store-border hover:border-store-primary hover:shadow-store-md transition-all duration-200 bg-store-background store-focus-ring min-w-auto'
                  onClick={() => setSelectedSubCategoryId(subcategory.id)}
                >
                  <div className='flex items-center justify-between min-w-auto'>
                    <div className='flex-1 min-w-0'>
                      <h4 className='font-medium text-store-text group-hover:text-store-primary transition-colors mb-1 truncate'>
                        {subcategory.name}
                      </h4>
                      {/* <p className='text-sm text-store-text-light truncate'>
                        {subcategory.productCount.toLocaleString()} items
                      </p> */}
                    </div>
                    <ArrowRightIcon className='h-4 w-4 store-text-light group-hover:store-text-primary transform group-hover:translate-x-1 transition-all duration-200 flex-shrink-0 ml-2' />
                  </div>
                </Link>
              ))}
            </div>

            {/* Show "View All" if there are many subcategories */}
            {activeCategoryData.subcategories.length > 8 && (
              <div className='text-center mt-6 pt-4 border-t border-gray-100'>
                <Link
                  href={activeCategoryData.href}
                  className='inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200'
                  onClick={() => setSelectedCategoryId(activeCategoryData.id)}
                >
                  Explore All {activeCategoryData.name}
                  <ArrowRightIcon className='h-4 w-4 ml-2' />
                </Link>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default React.memo(MegaMenu);
