'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/common/ToastProvider';
import {
  Box,
  Container,
  Typography,
  LinearProgress,
  Button,
  Card,
  CardContent,
  Fade,
  Slide,
} from '@mui/material';
import {
  ArrowForward,
  ArrowBack,
  CheckCircle,
  Store,
  Inventory,
  CloudUpload,
  Rocket,
  Info,
} from '@mui/icons-material';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import StoreConfigurationStep from './StoreConfigurationStep';
import AddProductStep from './AddProductStep';
import BulkUploadStep from './BulkUploadStep';
import { useOnboardingStore } from '@/stores/onboardingStore';
import {
  createStoreConfiguration,
  getStoreConfiguration,
  updateStoreConfiguration,
} from '@/lib/api/store-config';
import { useMedusaBackendProducts } from '@/hooks/useMedusaAdminBackend';
import medusaAdminAPI from '@/lib/medusa-admin-api';
import { useLoadingBackdrop } from '@/contexts/LoadingBackdropContext';
import { useBulkImport } from '@/hooks/useBulkImport';

// Modern theme for onboarding
const onboardingTheme = createTheme({
  palette: {
    primary: {
      main: '#2563eb',
      light: '#3b82f6',
      dark: '#1d4ed8',
    },
    secondary: {
      main: '#10b981',
      light: '#34d399',
      dark: '#059669',
    },
    background: {
      default: '#f8fafc',
      paper: '#ffffff',
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h4: {
      fontWeight: 700,
      fontSize: '1.875rem',
    },
    h5: {
      fontWeight: 600,
      fontSize: '1.5rem',
    },
    h6: {
      fontWeight: 600,
      fontSize: '1.25rem',
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          textTransform: 'none',
          fontWeight: 600,
          padding: '12px 24px',
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 16,
          boxShadow:
            '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
        },
      },
    },
    MuiLinearProgress: {
      styleOverrides: {
        root: {
          height: 8,
          borderRadius: 4,
        },
      },
    },
  },
});

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
}

interface ProductData {
  name: string;
  price: string;
  description: string;
  category: string;
  sku?: string;
  inventory_quantity: string;
}

export const filteredPayload = <T,>(v: T): T => {
  const g = (x: any): any =>
    x == null
      ? undefined
      : typeof x === 'string'
        ? ((x = x.trim()), x || undefined)
        : Array.isArray(x)
          ? ((x = x.map(g).filter(y => y !== undefined)),
            x.length ? x : undefined)
          : x && x.constructor === Object
            ? ((x = Object.fromEntries(
                Object.entries(x)
                  .map(([k, v]) => [k, g(v)])
                  .filter(([, v]) => v !== undefined)
              )),
              Object.keys(x).length ? x : undefined)
            : x;
  return (g(v) ?? ({} as T)) as T;
};

const OnboardingWizard: React.FC = () => {
  const router = useRouter();
  const { showLoading, hideLoading } = useLoadingBackdrop();

  const {
    user,
    updateUser,
    updateOnboardingStatus,
    completeOnboarding: completeAuthOnboarding,
  } = useAuth();
  const toast = useToast();
  const { isUploading, uploadProgress, uploadError, importStatus, uploadFile } =
    useBulkImport();

  // Use Zustand store
  const {
    currentStep,
    storeData,
    productData,
    bulkUploadFile,
    isLoading,
    setCurrentStep,
    updateStoreData,
    updateProductData,
    setBulkUploadFile,
    setError,
    setLoading,
    handleBulkUpload,
    completeOnboarding,
    // Smart navigation methods
    cacheStepData,
    hasStepDataChanged,
    setHasUnsavedChanges,
    // Validation methods
    validateForm,
    isCreateOperation,
    isUpdateOperation,
    // Backend integration methods
    initializeFromBackend,
    updateBackendOnboardingStatus,
  } = useOnboardingStore();

  // Get current step data for change detection
  const getCurrentStepData = React.useCallback(() => {
    switch (currentStep) {
      case 0:
        return storeData;
      case 1:
        return productData;
      case 2:
        return { bulkUploadFile: bulkUploadFile?.name || null };
      default:
        return {};
    }
  }, [currentStep, storeData, productData, bulkUploadFile]);

  // Initialize onboarding state from backend on mount
  React.useEffect(() => {
    if (user) {
      // Initialize from backend with user's onboarding status
      initializeFromBackend(user);
    }
  }, [user, initializeFromBackend]);

  // Track changes in current step data
  React.useEffect(() => {
    const currentData = getCurrentStepData();
    const dataHasChanged = hasStepDataChanged(currentStep, currentData);
    setHasUnsavedChanges(dataHasChanged);
  }, [
    getCurrentStepData,
    currentStep,
    hasStepDataChanged,
    setHasUnsavedChanges,
  ]);

  const steps: OnboardingStep[] = [
    {
      id: 'store-configuration',
      title: 'Store Configuration',
      description: 'Set up your store details and branding',
      icon: <Store sx={{ fontSize: 28 }} />,
    },
    {
      id: 'add-product',
      title: 'Add Product',
      description: 'Add your first product to get started',
      icon: <Inventory sx={{ fontSize: 28 }} />,
    },
    {
      id: 'bulk-upload',
      title: 'Bulk Upload (Optional)',
      description: 'Upload multiple products at once',
      icon: <CloudUpload sx={{ fontSize: 28 }} />,
    },
  ];

  const progress = ((currentStep + 1) / steps.length) * 100;

  // Enhanced navigation with validation and create/update logic
  const nextStep = async () => {
    if (currentStep < steps.length - 1) {
      try {
        setLoading(true);
        setError(null);

        const currentData = getCurrentStepData();
        const dataHasChanged = hasStepDataChanged(currentStep, currentData);

        console.log(`🔍 Step ${currentStep} analysis:`, {
          dataHasChanged,
          currentData,
        });

        // Only save if data has changed or no cache exists
        if (dataHasChanged) {
          console.log('💾 Saving step data due to changes...');

          // Save progress for current step and update backend
          if (currentStep === 0) {
            // Store Configuration Step - Enhanced with validation and create/update logic
            console.log('🏪 Processing store configuration...');

            // Validate form first
            const validation = validateForm();
            if (!validation.isValid) {
              console.error('❌ Form validation failed:', validation.errors);
              toast.error('Please fix the validation errors before proceeding');
              // setError('Please fix the validation errors before proceeding');
              return;
            }

            // Determine operation type
            const isCreate = isCreateOperation();
            const isUpdate = isUpdateOperation();

            console.log('🔍 Store configuration operation:', {
              isCreate,
              isUpdate,
              storeId: storeData.id,
            });
            // Prepare payload with required fields
            const updatedPayload = filteredPayload(storeData);
            console.log('updatedPayload::::::::::::', updatedPayload);
            const payload = {
              ...updatedPayload,
              created_by_user: user?.first_name + ' ' + user?.last_name,
              user_id: storeData.user_id || user?.id || 'default-user',
              onboarding_step: 1,
              store_status: 'active', // Add required field
              // Ensure optional fields have default values
              address_line_2: storeData.address_line_2 || '',
              website: storeData.website || '',
            };

            let data;

            if (isCreate) {
              console.log('🆕 Creating new store configuration...');
              data = await createStoreConfiguration(payload);
            } else if (isUpdate) {
              delete payload.store_handle;
              console.log('🔄 Updating existing store configuration...');
              data = await updateStoreConfiguration(
                'xkradafovz2xj964ava7qbeg',
                payload
              );
            }

            console.log('📊 API Response:', data);

            if (data?.data?.id) {
              // Update store data with the returned information
              updateStoreData({
                id: data.data.documentId || data.data.id,
                store_name: data.data.store_name,
                store_handle: data.data.store_handle,
                store_description: data.data.store_description,
                store_email: data.data.store_email,
                gst_number: data.data.gst_number,
                business_type: data.data.business_type,
                business_category: data.data.business_category,
                email: data.data.email,
                phone: data.data.phone,
                website: data.data.website,
                address_line_1: data.data.address_line_1,
                address_line_2: data.data.address_line_2,
                city: data.data.city,
                state: data.data.state,
                pincode: data.data.pincode,
                country: data.data.country,
                store_theme: data.data.store_theme,
                payment_methods: {
                  cash_on_delivery: data.data.payment_methods?.cash_on_delivery,
                  upi: data.data.payment_methods?.upi,
                  credit_card: data.data.payment_methods.credit_card,
                  debit_card: data.data.payment_methods.debit_card,
                  net_banking: data.data.payment_methods.net_banking,
                  wallet: data.data.payment_methods.wallet,
                  bnpl: data.data.payment_methods.bnpl,
                },
                store_logo_url: data.data.store_logo_url,
                user_id: '',
                // onboarding_step: 1,
                // onboarding_completed: false,
              });
              await updateBackendOnboardingStatus({
                onboarding_store_config: true,
              });
            }

            // Update backend onboarding status
            await medusaAdminAPI.updateAdminUserDetails(user?.id, {
              metadata: {
                onboarding_store_config: true,
              },
            });

            toast.success('Store configuration saved successfully!');
          } else if (currentStep === 1) {
            // Product Step - Enhanced with proper type handling
            console.log('�️ Processing product data...');

            // Type guard to ensure we have product data
            const productInfo = currentData as ProductData;

            const payload: Record<string, any> = {
              title: productInfo.name,
              handle: productInfo.sku,
              description: productInfo.description,

              status: 'draft',
              options: [
                {
                  title: 'default',
                  values: ['default'],
                },
              ],
              metadata: {
                additional_data: {
                  // images: imgMode === 'url' ? imgUrls : imagePreviewUrls,
                  product_prices: [
                    {
                      sale_price: Number(productInfo.price) || 0,
                      original_price: Number(productInfo.price) || 0,
                    },
                  ],
                  product_quantity: Number(productInfo.inventory_quantity) || 0,
                  product_inventory_status: 'in_stock',
                  product_overview: '',
                  product_features: '',
                  product_specifications: '',
                },
              },
            };
            // const response = await createProduct(
            //   payload,
            //   storeData.store_handle || 'default-store'
            // );
            // console.log('currentData:::::::::::', currentData, response);
            // Update backend onboarding status
            await updateOnboardingStatus({
              onboarding_add_product: true,
            });
            await medusaAdminAPI.updateAdminUserDetails(user?.id, {
              metadata: {
                onboarding_add_product: true,
              },
            });
            // toast.success('Product added successfully!');
          }

          // Cache the data after successful save
          cacheStepData(currentStep, currentData);
          setHasUnsavedChanges(false);
        } else {
          console.log('⚡ Skipping save - no changes detected');
        }

        setCurrentStep(currentStep + 1);
      } catch (error) {
        console.error('Failed to save step:', error);
        toast.error('Failed to save store configuration. Please try again.');
        // setError(error instanceof Error ? error.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    }
  };

  // Smart back navigation: cache current data before going back
  const prevStep = () => {
    if (currentStep > 0) {
      const currentData = getCurrentStepData();
      cacheStepData(currentStep, currentData);
      setCurrentStep(currentStep - 1);
    }
  };

  // State for bulk upload actions
  const [bulkUploadCompleted, setBulkUploadCompleted] = React.useState(false);
  const [bulkUploadSkipped, setBulkUploadSkipped] = React.useState(false);

  const handleBulkUploadAction = async (action: 'upload' | 'skip') => {
    try {
      setLoading(true);

      if (action === 'upload') {
        // Handle bulk upload if file is selected
        const response = await uploadFile(bulkUploadFile, user?.store_handle);
        console.log('::::::::response:::', response);
        // await handleBulkUpload();
        setBulkUploadCompleted(true);
        toast.success('Bulk upload completed successfully!');
      } else {
        // Skip bulk upload
        setBulkUploadSkipped(true);
        toast.info(
          'Bulk upload skipped. You can upload products later from the admin dashboard.'
        );
      }

      // Update backend to mark bulk upload step as completed
      await updateOnboardingStatus({
        onboarding_bulk_upload: true,
      });

      if (user?.id) {
        await medusaAdminAPI.updateAdminUserDetails(user.id, {
          metadata: {
            onboarding_bulk_upload: true,
          },
        });
      }
    } catch (error) {
      console.error('Bulk upload action failed:', error);
      toast.error(`Failed to ${action} bulk upload. Please try again.`);
    } finally {
      setLoading(false);
    }
  };

  const handleFinish = async () => {
    try {
      setLoading(true);

      // Update backend onboarding status to completed
      let response = null;
      if (user?.id) {
        response = await medusaAdminAPI.updateAdminUserDetails(user.id, {
          metadata: {
            onboarding_status: 'completed',
          },
        });
      }

      console.log('onboarding response ----------', response);

      // Complete onboarding in both stores and backend
      // await completeOnboarding();
      // await completeAuthOnboarding();

      // Update user to remove isNewUser flag
      if (user) {
        updateUser({ isNewUser: false });
      }

      toast.success(
        '🎉 Onboarding completed successfully! Welcome to your admin dashboard.'
      );

      // Use the store_handle from current user data or storeData as fallback
      const storeHandle =
        user?.store_handle || storeData.store_handle || 'default-store';
      console.log('Redirecting to:', `/${storeHandle}/admin`);

      router.push(`/${storeHandle}/admin`);
    } catch (error) {
      console.error('Onboarding completion failed:', error);
      toast.error('Failed to complete onboarding. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const isStepValid = () => {
    switch (currentStep) {
      case 0: // Store Configuration
        return (
          storeData.store_name.trim() &&
          // storeData.phone.trim() &&
          storeData.business_type &&
          storeData.business_category
          // storeData.store_description.trim() &&
          // storeData.gst_number.trim() &&
          // storeData.email.trim() &&
          // storeData.address_line_1.trim() &&
          // storeData.city.trim() &&
          // storeData.state.trim() &&
          // storeData.pincode.trim() &&
        );
      case 1: // Add Product
        return (
          productData.name.trim() &&
          productData.price.trim() &&
          productData.description.trim() &&
          productData.category.trim() &&
          productData.inventory_quantity.trim()
        );
      case 2: // Bulk Upload (Optional)
        return true; // This step is optional
      default:
        return true;
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <StoreConfigurationStep
            data={storeData}
            updateData={(field, value) => updateStoreData({ [field]: value })}
          />
        );

      case 1:
        return (
          <AddProductStep
            data={productData}
            updateData={(field, value) => updateProductData({ [field]: value })}
          />
        );

      case 2:
        return (
          <Box>
            <BulkUploadStep file={bulkUploadFile} setFile={setBulkUploadFile} />

            {/* Status Messages */}
            {bulkUploadCompleted && (
              <Box sx={{ mt: 3 }}>
                <Card
                  sx={{
                    bgcolor: 'success.50',
                    border: '1px solid',
                    borderColor: 'success.200',
                  }}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Box display='flex' alignItems='center' gap={2}>
                      <CheckCircle
                        sx={{ color: 'success.main', fontSize: 32 }}
                      />
                      <Box>
                        <Typography
                          variant='h6'
                          color='success.main'
                          fontWeight={600}
                        >
                          Bulk Upload Completed!
                        </Typography>
                        <Typography variant='body2' color='success.dark'>
                          Your products have been successfully uploaded to your
                          store.
                        </Typography>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Box>
            )}

            {bulkUploadSkipped && (
              <Box sx={{ mt: 3 }}>
                <Card
                  sx={{
                    bgcolor: 'info.50',
                    border: '1px solid',
                    borderColor: 'info.200',
                  }}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Box display='flex' alignItems='center' gap={2}>
                      <Info sx={{ color: 'info.main', fontSize: 32 }} />
                      <Box>
                        <Typography
                          variant='h6'
                          color='info.main'
                          fontWeight={600}
                        >
                          Bulk Upload Skipped
                        </Typography>
                        <Typography variant='body2' color='info.dark'>
                          You can upload products later from your admin
                          dashboard.
                        </Typography>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Box>
            )}
          </Box>
        );

      default:
        return null;
    }
  };

  const fetchStoreConfig = async (storeHandle: string) => {
    try {
      console.log('fetchStoreConfig:::storeHandle:::', storeHandle);
      const data = await getStoreConfiguration(storeHandle);
      const storeData = data?.data[0];
      if (storeData)
        updateStoreData({
          id: storeData?.documentId || storeData?.id || '',
          store_name: storeData?.store_name || '',
          store_handle: storeData?.store_handle,
          store_description: storeData?.store_description || '',
          store_email: storeData?.store_email || '',
          gst_number: storeData?.gst_number || '',
          business_type: storeData?.business_type || '',
          business_category: storeData?.business_category || '',
          email: storeData?.email || '',
          phone: storeData?.phone || '',
          website: storeData?.website || '',
          address_line_1: storeData?.address_line_1 || '',
          address_line_2: storeData?.address_line_2 || '',
          city: storeData?.city || '',
          state: storeData?.state || '',
          pincode: storeData?.pincode || '',
          country: storeData?.country || '',
          // store_theme: storeData?.store_theme,
          payment_methods: {
            cash_on_delivery: storeData?.payment_methods?.cash_on_delivery,
            upi: storeData?.payment_methods?.upi,
            credit_card: storeData?.payment_methods.credit_card,
            debit_card: storeData?.payment_methods.debit_card,
            net_banking: storeData?.payment_methods.net_banking,
            wallet: storeData?.payment_methods.wallet,
            bnpl: storeData?.payment_methods.bnpl,
          },
          user_id: '',
          store_logo_url: storeData?.store_logo_url || '',
          // onboarding_step: 1,
          // onboarding_completed: false,
        });
    } catch (error) {
      console.error('Error::::', error);
    }
  };
  // console.log('::::::::::data::::::::::', { user });
  useEffect(() => {
    hideLoading();
    if (user?.store_handle) fetchStoreConfig(user?.store_handle);
  }, [user?.store_handle]);

  return (
    <ThemeProvider theme={onboardingTheme}>
      <Box
        sx={{
          minHeight: '100vh',
          backgroundColor: '#f8fafc',
          py: 0,
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        <Container
          maxWidth='lg'
          sx={{ py: 4, position: 'relative', zIndex: 1 }}
        >
          {/* Header */}
          <Fade in timeout={800}>
            <Box textAlign='center' mb={6}>
              <Typography
                variant='h4'
                component='h1'
                sx={{
                  color: '#1e293b',
                  mb: 2,
                  fontWeight: 700,
                  textShadow: 'none',
                }}
              >
                Welcome to Your Store Setup
              </Typography>
              <Typography
                variant='h6'
                sx={{
                  color: '#64748b',
                  fontWeight: 400,
                  textShadow: '0 1px 2px rgba(0,0,0,0.1)',
                }}
              >
                Let&apos;s get your store ready for customers in just a few
                steps
              </Typography>
            </Box>
          </Fade>

          {/* Progress Bar */}
          <Slide in direction='down' timeout={1000}>
            <Card sx={{ mb: 4, overflow: 'visible' }}>
              <CardContent sx={{ p: 4 }}>
                <Box mb={3}>
                  <Box
                    display='flex'
                    justifyContent='space-between'
                    alignItems='center'
                    mb={2}
                  >
                    <Typography variant='h6' color='primary' fontWeight={600}>
                      Setup Progress
                    </Typography>
                    <Typography variant='body2' color='text.secondary'>
                      Step {currentStep + 1} of {steps.length}
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant='determinate'
                    value={progress}
                    sx={{
                      mb: 3,
                      '& .MuiLinearProgress-bar': {
                        background:
                          'linear-gradient(90deg, #2563eb 0%, #10b981 100%)',
                      },
                    }}
                  />

                  {/* Step indicators */}
                  <Box
                    display='flex'
                    justifyContent='space-between'
                    alignItems='center'
                  >
                    {steps.map((step, index) => (
                      <Box
                        key={step.id}
                        display='flex'
                        flexDirection='column'
                        alignItems='center'
                        flex={1}
                      >
                        <Box
                          sx={{
                            width: 56,
                            height: 56,
                            borderRadius: '50%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            mb: 2,
                            background:
                              index < currentStep
                                ? 'linear-gradient(135deg, #10b981 0%, #059669 100%)'
                                : index === currentStep
                                  ? 'linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%)'
                                  : '#f1f5f9',
                            color: index <= currentStep ? 'white' : '#64748b',
                            boxShadow:
                              index <= currentStep
                                ? '0 4px 12px rgba(37, 99, 235, 0.3)'
                                : '0 2px 4px rgba(0, 0, 0, 0.1)',
                            transition: 'all 0.3s ease',
                          }}
                        >
                          {index < currentStep ? (
                            <CheckCircle sx={{ fontSize: 28 }} />
                          ) : (
                            step.icon
                          )}
                        </Box>
                        <Typography
                          variant='body2'
                          fontWeight={index <= currentStep ? 600 : 400}
                          color={
                            index <= currentStep ? 'primary' : 'text.secondary'
                          }
                          textAlign='center'
                          sx={{ maxWidth: 120 }}
                        >
                          {step.title}
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                </Box>

                {/* Current step title and description */}
                <Box textAlign='center' mt={4}>
                  <Typography
                    variant='h5'
                    fontWeight={600}
                    color='primary'
                    mb={1}
                  >
                    {steps[currentStep]?.title || 'Loading...'}
                  </Typography>
                  <Typography variant='body1' color='text.secondary'>
                    {steps[currentStep]?.description || 'Please wait...'}
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Slide>

          {/* Step Content */}
          <Slide in direction='up' timeout={800}>
            <Card sx={{ mb: 4 }}>
              <CardContent sx={{ p: 4 }}>{renderStepContent()}</CardContent>
            </Card>
          </Slide>

          {/* Navigation */}
          <Box
            display='flex'
            justifyContent='space-between'
            alignItems='center'
          >
            <Button
              onClick={prevStep}
              disabled={currentStep === 0}
              variant='outlined'
              startIcon={<ArrowBack />}
              sx={{
                minWidth: 120,
                visibility: currentStep === 0 ? 'hidden' : 'visible',
              }}
            >
              Previous
            </Button>

            {currentStep === steps.length - 1 ? (
              // Bulk Upload Step - Show Upload/Skip buttons or Launch Store button
              <Box display='flex' gap={2}>
                {!bulkUploadCompleted && !bulkUploadSkipped ? (
                  // Show Upload and Skip buttons
                  <>
                    <Button
                      onClick={() => handleBulkUploadAction('skip')}
                      disabled={isLoading}
                      variant='outlined'
                      size='large'
                      sx={{ minWidth: 120 }}
                    >
                      {isLoading ? 'Processing...' : 'Skip Upload'}
                    </Button>
                    <Button
                      onClick={() => handleBulkUploadAction('upload')}
                      disabled={isLoading || !bulkUploadFile}
                      variant='contained'
                      size='large'
                      startIcon={<CloudUpload />}
                      sx={{ minWidth: 160 }}
                    >
                      {isLoading ? 'Uploading...' : 'Upload Products'}
                    </Button>
                  </>
                ) : (
                  // Show Launch Store button after upload/skip
                  <Button
                    onClick={handleFinish}
                    disabled={isLoading}
                    variant='contained'
                    size='large'
                    startIcon={isLoading ? null : <Rocket />}
                    sx={{
                      minWidth: 160,
                      background:
                        'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                      '&:hover': {
                        background:
                          'linear-gradient(135deg, #059669 0%, #047857 100%)',
                      },
                    }}
                  >
                    {isLoading ? (
                      <>
                        <Box
                          sx={{
                            width: 20,
                            height: 20,
                            border: '2px solid rgba(255,255,255,0.3)',
                            borderTop: '2px solid white',
                            borderRadius: '50%',
                            animation: 'spin 1s linear infinite',
                            mr: 1,
                            '@keyframes spin': {
                              '0%': { transform: 'rotate(0deg)' },
                              '100%': { transform: 'rotate(360deg)' },
                            },
                          }}
                        />
                        Launching...
                      </>
                    ) : (
                      'Launch Store'
                    )}
                  </Button>
                )}
              </Box>
            ) : (
              <Button
                onClick={nextStep}
                disabled={!isStepValid()}
                variant='contained'
                size='large'
                endIcon={<ArrowForward />}
                sx={{ minWidth: 120 }}
              >
                Next
              </Button>
            )}
          </Box>
        </Container>
      </Box>
    </ThemeProvider>
  );
};

export default OnboardingWizard;
