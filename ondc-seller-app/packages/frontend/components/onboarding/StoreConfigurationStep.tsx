'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Grid,
  TextField,
  Typography,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  OutlinedInput,
  FormControlLabel,
  Switch,
  Avatar,
  Button,
  IconButton,
  Divider,
  Paper,
  Stack,
  Alert,
  FormHelperText,
  Tooltip,
  CircularProgress,
} from '@mui/material';
import {
  Business,
  LocationOn,
  Phone,
  Email,
  Description,
  Palette,
  CloudUpload,
  Payment,
  CheckCircle,
  AutoAwesome,
  Info,
} from '@mui/icons-material';
import StoreLogoDropzone from '../StoreLogoDropzone';
import { useOnboardingStore } from '@/stores/onboardingStore';
import { type ColorPalette } from '@/lib/utils/colorPaletteExtractor';
import {
  BUSINESS_TYPES,
  BUSINESS_CATEGORIES,
  STORE_THEMES,
} from '@/lib/validation/onboarding-schemas';

interface StoreConfigurationData {
  store_name: string;
  store_description: string;
  gst_number: string;
  business_type: string;
  business_category: string;
  email: string;
  phone: string;
  website?: string;
  address_line_1: string;
  address_line_2?: string;
  city: string;
  state: string;
  pincode: string;
  country: string;
  store_theme: string;
  store_logo?: File;
  store_logo_url?: string;
  store_color_palette?: {
    vibrant: string | null;
    vibrantLight: string | null;
    vibrantDark: string | null;
    muted: string | null;
    mutedLight: string | null;
    mutedDark: string | null;
    dominant: string | null;
    population: number;
    extractedAt: string;
  };
  payment_methods?: { [key: string]: boolean };
  store_handle?: string;
}

interface PaymentMode {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  enabled: boolean;
}

interface StoreConfigurationStepProps {
  data: StoreConfigurationData;
  updateData: (field: keyof StoreConfigurationData, value: any) => void;
}

const businessTypes = [
  { value: 'individual', label: 'Individual' },
  { value: 'partnership', label: 'Partnership' },
  { value: 'private_limited', label: 'Private Limited' },
  { value: 'public_limited', label: 'Public Limited' },
  { value: 'llp', label: 'Limited Liability Partnership (LLP)' },
  { value: 'proprietorship', label: 'Proprietorship' },
];

const businessCategories = [
  { value: 'fashion', label: 'Fashion & Apparel' },
  { value: 'electronics', label: 'Electronics' },
  { value: 'home_garden', label: 'Home & Garden' },
  { value: 'health_beauty', label: 'Health & Beauty' },
  { value: 'sports_outdoors', label: 'Sports & Outdoors' },
  { value: 'books_media', label: 'Books & Media' },
  { value: 'food_beverages', label: 'Food & Beverages' },
  { value: 'automotive', label: 'Automotive' },
  { value: 'toys_games', label: 'Toys & Games' },
  { value: 'jewelry_accessories', label: 'Jewelry & Accessories' },
  { value: 'other', label: 'Other' },
];

// Default payment modes available in the system
const defaultPaymentModes: PaymentMode[] = [
  {
    id: 'cash_on_delivery',
    name: 'Cash on Delivery',
    description: 'Accept cash payments upon delivery',
    icon: <Payment />,
    enabled: true,
  },
  {
    id: 'upi',
    name: 'UPI',
    description: 'Accept UPI payments (PhonePe, GPay, Paytm)',
    icon: <Payment />,
    enabled: true,
  },
  {
    id: 'credit_card',
    name: 'Credit Card',
    description: 'Accept Visa, MasterCard, American Express',
    icon: <Payment />,
    enabled: true,
  },
  {
    id: 'debit_card',
    name: 'Debit Card',
    description: 'Accept debit card payments',
    icon: <Payment />,
    enabled: true,
  },
  {
    id: 'net_banking',
    name: 'Net Banking',
    description: 'Accept payments via internet banking',
    icon: <Payment />,
    enabled: false,
  },
  {
    id: 'wallet',
    name: 'Digital Wallets',
    description: 'Accept payments from digital wallets',
    icon: <Payment />,
    enabled: false,
  },
  {
    id: 'bnpl',
    name: 'Buy Now Pay Later',
    description: 'Accept BNPL payments (Simpl, LazyPay)',
    icon: <Payment />,
    enabled: false,
  },
];

const StoreConfigurationStep: React.FC<StoreConfigurationStepProps> = ({
  data,
  updateData,
}) => {
  const [storeLogo, setStoreLogo] = useState<File | null>(null);
  const [storeLogoUrl, setStoreLogoUrl] = useState<string>('');
  const [paymentModes, setPaymentModes] = useState<PaymentMode[]>(
    defaultPaymentModes.map(mode => ({
      ...mode,
      enabled: data.payment_methods?.[mode.id] ?? mode.enabled,
    }))
  );

  // Get validation and auto-fill state from store
  const {
    validationState,
    validateField,
    setFieldTouched,
    isAutoFilled,
    autoFillSource,
    autoFillFromAuthStorage,
    autoFillFromLocalStorage,
  } = useOnboardingStore();

  // Auto-fill on component mount
  useEffect(() => {
    if (!isAutoFilled) {
      console.log('🔄 Attempting auto-fill on component mount...');
      autoFillFromAuthStorage();

      // If auth store didn't provide data, try localStorage
      const store = useOnboardingStore.getState();
      if (!store.isAutoFilled) {
        autoFillFromLocalStorage();
      }
    }
  }, [isAutoFilled, autoFillFromAuthStorage, autoFillFromLocalStorage]);

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      updateData('store_logo', file);
    }
  };

  const handlePaymentModeToggle = (modeId: string, enabled: boolean) => {
    const updatedModes = paymentModes.map(mode =>
      mode.id === modeId ? { ...mode, enabled } : mode
    );
    setPaymentModes(updatedModes);

    // Update the data with the new payment modes configuration
    const paymentModesConfig = updatedModes.reduce(
      (acc, mode) => {
        acc[mode.id] = mode.enabled;
        return acc;
      },
      {} as { [key: string]: boolean }
    );

    updateData('payment_methods', paymentModesConfig);
  };

  // Enhanced field change handler with validation
  const handleFieldChange = (
    fieldName: keyof StoreConfigurationData,
    value: any
  ) => {
    // Update the data
    updateData(fieldName, value);

    // Mark field as touched
    setFieldTouched(fieldName);

    // Validate the field
    validateField(fieldName, value);

    // Auto-generate store handle when store name changes
    if (fieldName === 'store_name' && typeof value === 'string') {
      const handle = createHandle(value);
      updateData('store_handle', handle);
    }
  };

  const createHandle = (name: string) =>
    name
      .toLowerCase()
      .trim()
      .replace(/[\s\W-]+/g, '-') // Replace spaces and non-word chars with hyphens
      .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens

  // Get field error helper
  const getFieldError = (fieldName: string) => {
    return validationState.touchedFields.has(fieldName)
      ? validationState.errors[fieldName]
      : '';
  };

  // Check if field has error
  const hasFieldError = (fieldName: string) => {
    return (
      validationState.touchedFields.has(fieldName) &&
      !!validationState.errors[fieldName]
    );
  };
  useEffect(() => {
    if (storeLogoUrl) updateData('store_logo_url', storeLogoUrl);
  }, [storeLogoUrl]);

  // Handle color palette extraction
  const handleColorPaletteExtracted = useCallback(
    (palette: ColorPalette | null) => {
      console.log(
        '🎨 [StoreConfigurationStep] Color palette extracted:',
        palette
      );
      updateData('store_color_palette', palette);
    },
    [updateData]
  );

  return (
    <Box sx={{ maxWidth: 1000, mx: 'auto' }}>
      <Stack spacing={6}>
        {/* Auto-fill Indicator */}
        {isAutoFilled && (
          <Alert
            severity='info'
            icon={<AutoAwesome />}
            sx={{
              borderRadius: 2,
              '& .MuiAlert-message': {
                display: 'flex',
                alignItems: 'center',
                gap: 1,
              },
            }}
          >
            <Box display='flex' alignItems='center' gap={1}>
              <Typography variant='body2' fontWeight={600}>
                Auto-filled from{' '}
                {autoFillSource === 'authStore'
                  ? 'your account'
                  : autoFillSource === 'localStorage'
                    ? 'saved data'
                    : 'backend'}
              </Typography>
              <Tooltip title='Some fields have been automatically filled based on your account information. You can modify them as needed.'>
                <Info fontSize='small' />
              </Tooltip>
            </Box>
          </Alert>
        )}
        {/* Basic Store Information */}
        <Card
          elevation={8}
          sx={{
            borderRadius: 3,
            border: '1px solid #e2e8f0',
            backgroundColor: '#ffffff',
            '&:hover': {
              boxShadow:
                '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
            },
          }}
        >
          <CardContent sx={{ p: 4 }}>
            <Box display='flex' alignItems='center' mb={3}>
              <Business color='primary' sx={{ mr: 2 }} />
              <Typography variant='h6' fontWeight={600} color='primary'>
                Basic Store Information
              </Typography>
            </Box>

            <Grid container spacing={3}>
              <Grid item size={{ xs: 12, md: 4 }}>
                <TextField
                  fullWidth
                  label='Store Name'
                  value={data.store_name}
                  onChange={e =>
                    handleFieldChange('store_name', e.target.value)
                  }
                  placeholder='Enter your store name'
                  required
                  variant='outlined'
                  error={hasFieldError('store_name')}
                  helperText={getFieldError('store_name')}
                  InputProps={{
                    endAdornment: isAutoFilled && data.store_name && (
                      <Tooltip title='Auto-filled'>
                        <AutoAwesome color='primary' fontSize='small' />
                      </Tooltip>
                    ),
                  }}
                />
              </Grid>

              <Grid item size={{ xs: 12, md: 4 }}>
                <TextField
                  fullWidth
                  label='Store Handle'
                  value={data.store_handle}
                  onChange={e =>
                    handleFieldChange('store_handle', e.target.value)
                  }
                  placeholder='your-store-handle'
                  required
                  variant='outlined'
                  error={hasFieldError('store_handle')}
                  helperText={
                    getFieldError('store_handle') ||
                    'This will be your store URL'
                  }
                  InputProps={{
                    startAdornment: (
                      <Typography variant='body2' color='text.secondary'>
                        store.com/
                      </Typography>
                    ),
                  }}
                />
              </Grid>

              <Grid item size={{ xs: 12, md: 4 }}>
                <TextField
                  fullWidth
                  label='GST Number'
                  value={data.gst_number}
                  onChange={e =>
                    handleFieldChange('gst_number', e.target.value)
                  }
                  placeholder='12ABCDE1234F1Z5'
                  inputProps={{ maxLength: 15 }}
                  // required
                  variant='outlined'
                  error={hasFieldError('gst_number')}
                  helperText={getFieldError('gst_number')}
                />
              </Grid>

              <Grid item size={{ xs: 12 }}>
                <TextField
                  fullWidth
                  label='Store Description'
                  value={data.store_description}
                  onChange={e =>
                    handleFieldChange('store_description', e.target.value)
                  }
                  placeholder='Describe what your store sells and what makes it unique'
                  multiline
                  rows={3}
                  // required
                  variant='outlined'
                  error={hasFieldError('store_description')}
                  helperText={getFieldError('store_description')}
                />
              </Grid>

              <Grid item size={{ xs: 12, md: 4 }}>
                <TextField
                  fullWidth
                  label='Store Email Address'
                  type='email'
                  value={data?.store_email}
                  onChange={e =>
                    handleFieldChange('store_email', e.target.value)
                  }
                  placeholder='<EMAIL>'
                  // required
                  variant='outlined'
                  error={hasFieldError('store_email')}
                  helperText={getFieldError('store_email')}
                />
              </Grid>
              <Grid item size={{ xs: 12, md: 4 }}>
                <FormControl
                  fullWidth
                  required
                  variant='outlined'
                  error={hasFieldError('business_type')}
                >
                  <InputLabel>Business Type</InputLabel>
                  <Select
                    value={data.business_type}
                    onChange={e =>
                      handleFieldChange('business_type', e.target.value)
                    }
                    label='Business Type'
                  >
                    {businessTypes.map(type => (
                      <MenuItem key={type.value} value={type.value}>
                        {type.label}
                      </MenuItem>
                    ))}
                  </Select>
                  {hasFieldError('business_type') && (
                    <FormHelperText>
                      {getFieldError('business_type')}
                    </FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid item size={{ xs: 12, md: 4 }}>
                <FormControl
                  fullWidth
                  required
                  variant='outlined'
                  error={hasFieldError('business_category')}
                >
                  <InputLabel>Business Category</InputLabel>
                  <Select
                    value={data.business_category}
                    onChange={e =>
                      handleFieldChange('business_category', e.target.value)
                    }
                    label='Business Category'
                  >
                    {businessCategories.map(category => (
                      <MenuItem key={category.value} value={category.value}>
                        {category.label}
                      </MenuItem>
                    ))}
                  </Select>
                  {hasFieldError('business_category') && (
                    <FormHelperText>
                      {getFieldError('business_category')}
                    </FormHelperText>
                  )}
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Payment Modes */}
        <Card
          elevation={8}
          sx={{
            borderRadius: 3,
            border: '1px solid #e2e8f0',
            backgroundColor: '#ffffff',
            '&:hover': {
              boxShadow:
                '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
            },
          }}
        >
          <CardContent sx={{ p: 4 }}>
            <Box display='flex' alignItems='center' mb={3}>
              <Payment color='primary' sx={{ mr: 2 }} />
              <Typography variant='h6' fontWeight={600} color='primary'>
                Payment Modes
              </Typography>
            </Box>

            <Typography variant='body2' color='text.secondary' mb={3}>
              Select the payment methods you want to accept from your customers
            </Typography>

            <Grid container spacing={2}>
              {paymentModes.map(mode => (
                <Grid item xs={12} sm={6} md={4} key={mode.id}>
                  <Paper
                    elevation={mode.enabled ? 3 : 1}
                    sx={{
                      p: 2,
                      border: mode.enabled ? '2px solid' : '1px solid',
                      borderColor: mode.enabled ? 'primary.main' : 'grey.300',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        elevation: 3,
                        borderColor: 'primary.main',
                      },
                    }}
                    onClick={() =>
                      handlePaymentModeToggle(mode.id, !mode.enabled)
                    }
                  >
                    <Box
                      display='flex'
                      alignItems='center'
                      justifyContent='space-between'
                    >
                      <Box display='flex' alignItems='center'>
                        <Box
                          sx={{
                            mr: 2,
                            color: mode.enabled ? 'primary.main' : 'grey.500',
                          }}
                        >
                          {mode.icon}
                        </Box>
                        <Box>
                          <Typography variant='subtitle2' fontWeight={600}>
                            {mode.name}
                          </Typography>
                          <Typography variant='caption' color='text.secondary'>
                            {mode.description}
                          </Typography>
                        </Box>
                      </Box>
                      <Switch
                        checked={mode.enabled}
                        onChange={e =>
                          handlePaymentModeToggle(mode.id, e.target.checked)
                        }
                        color='primary'
                        onClick={e => e.stopPropagation()}
                      />
                    </Box>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card
          elevation={8}
          sx={{
            borderRadius: 3,
            border: '1px solid #e2e8f0',
            backgroundColor: '#ffffff',
            '&:hover': {
              boxShadow:
                '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
            },
          }}
        >
          <CardContent sx={{ p: 4 }}>
            <Box display='flex' alignItems='center' mb={3}>
              <Phone color='primary' sx={{ mr: 2 }} />
              <Typography variant='h6' fontWeight={600} color='primary'>
                Contact Information
              </Typography>
            </Box>

            <Grid container spacing={3}>
              <Grid item size={{ xs: 12, md: 4 }}>
                <TextField
                  fullWidth
                  label='Email Address'
                  type='email'
                  value={data.email}
                  onChange={e => handleFieldChange('email', e.target.value)}
                  placeholder='<EMAIL>'
                  required
                  variant='outlined'
                  error={hasFieldError('email')}
                  helperText={getFieldError('email')}
                  InputProps={{
                    endAdornment: isAutoFilled && data.email && (
                      <Tooltip title='Auto-filled'>
                        <AutoAwesome color='primary' fontSize='small' />
                      </Tooltip>
                    ),
                  }}
                />
              </Grid>

              <Grid item size={{ xs: 12, md: 4 }}>
                <TextField
                  fullWidth
                  label='Phone Number'
                  type='tel'
                  value={data.phone}
                  onChange={e => handleFieldChange('phone', e.target.value)}
                  placeholder='+91 9876543210'
                  // required
                  variant='outlined'
                  error={hasFieldError('phone')}
                  helperText={getFieldError('phone')}
                  InputProps={{
                    endAdornment: isAutoFilled && data.phone && (
                      <Tooltip title='Auto-filled'>
                        <AutoAwesome color='primary' fontSize='small' />
                      </Tooltip>
                    ),
                  }}
                />
              </Grid>

              <Grid item size={{ xs: 12, md: 4 }}>
                <TextField
                  fullWidth
                  label='Website (Optional)'
                  type='url'
                  value={data.website || ''}
                  onChange={e => handleFieldChange('website', e.target.value)}
                  placeholder='https://yourwebsite.com'
                  variant='outlined'
                  error={hasFieldError('website')}
                  helperText={getFieldError('website')}
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Address Information */}
        <Card
          elevation={8}
          sx={{
            borderRadius: 3,
            border: '1px solid #e2e8f0',
            backgroundColor: '#ffffff',
            '&:hover': {
              boxShadow:
                '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
            },
          }}
        >
          <CardContent sx={{ p: 4 }}>
            <Box display='flex' alignItems='center' mb={3}>
              <LocationOn color='primary' sx={{ mr: 2 }} />
              <Typography variant='h6' fontWeight={600} color='primary'>
                Store Address
              </Typography>
            </Box>

            <Grid container spacing={3}>
              <Grid item size={{ xs: 12, md: 6 }}>
                <TextField
                  fullWidth
                  label='Address Line 1'
                  value={data.address_line_1}
                  onChange={e =>
                    handleFieldChange('address_line_1', e.target.value)
                  }
                  placeholder='Street address, building name, etc.'
                  // required
                  variant='outlined'
                  error={hasFieldError('address_line_1')}
                  helperText={getFieldError('address_line_1')}
                />
              </Grid>

              <Grid item size={{ xs: 12, md: 6 }}>
                <TextField
                  fullWidth
                  label='Address Line 2 (Optional)'
                  value={data.address_line_2 || ''}
                  onChange={e =>
                    handleFieldChange('address_line_2', e.target.value)
                  }
                  placeholder='Apartment, suite, unit, etc.'
                  variant='outlined'
                  error={hasFieldError('address_line_2')}
                  helperText={getFieldError('address_line_2')}
                />
              </Grid>

              <Grid item size={{ xs: 12, md: 4 }}>
                <TextField
                  fullWidth
                  label='City'
                  value={data.city}
                  onChange={e => handleFieldChange('city', e.target.value)}
                  placeholder='Mumbai'
                  // required
                  variant='outlined'
                  error={hasFieldError('city')}
                  helperText={getFieldError('city')}
                />
              </Grid>

              <Grid item size={{ xs: 12, md: 4 }}>
                <TextField
                  fullWidth
                  label='State'
                  value={data.state}
                  onChange={e => handleFieldChange('state', e.target.value)}
                  placeholder='Maharashtra'
                  // required
                  variant='outlined'
                  error={hasFieldError('state')}
                  helperText={getFieldError('state')}
                />
              </Grid>

              <Grid item size={{ xs: 12, md: 4 }}>
                <TextField
                  fullWidth
                  label='Pincode'
                  value={data.pincode}
                  onChange={e => handleFieldChange('pincode', e.target.value)}
                  placeholder='400001'
                  inputProps={{ maxLength: 6 }}
                  // required
                  variant='outlined'
                  error={hasFieldError('pincode')}
                  helperText={getFieldError('pincode')}
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Store Branding */}
        <Card
          elevation={8}
          sx={{
            borderRadius: 3,
            border: '1px solid #e2e8f0',
            backgroundColor: '#ffffff',
            '&:hover': {
              boxShadow:
                '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
            },
          }}
        >
          <CardContent sx={{ p: 4 }}>
            <Box display='flex' alignItems='center' mb={3}>
              <Palette color='primary' sx={{ mr: 2 }} />
              <Typography variant='h6' fontWeight={600} color='primary'>
                Store Branding
              </Typography>
            </Box>

            {/* <Grid container spacing={3} mb={3}>
              <Grid item size={{ xs: 12, md: 4 }}>
                <FormControl fullWidth>
                  <InputLabel>Select Theme</InputLabel>
                  <Select
                    value={data.store_theme}
                    onChange={e => updateData('store_theme', e.target.value)}
                    label='Select Theme'
                  >
                    {storeThemes.map(theme => (
                      <MenuItem key={theme.value} value={theme.value}>
                        <Box display='flex' alignItems='center'>
                          <Box
                            sx={{
                              width: 24,
                              height: 24,
                              borderRadius: '50%',
                              backgroundColor: theme.color,
                              mr: 2,
                            }}
                          />
                          {theme.label}
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            </Grid> */}
            <Grid container spacing={3} mb={3} size={12}>
              <Grid item size={12}>
                <StoreLogoDropzone
                  storeLogo={storeLogo}
                  setStoreLogo={setStoreLogo}
                  storeLogoUrl={storeLogoUrl}
                  setStoreLogoUrl={setStoreLogoUrl}
                  onColorPaletteExtracted={handleColorPaletteExtracted}
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Stack>
    </Box>
  );
};

export default StoreConfigurationStep;
