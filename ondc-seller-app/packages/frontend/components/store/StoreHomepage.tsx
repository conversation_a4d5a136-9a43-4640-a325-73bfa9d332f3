'use client';

import React, { useEffect, useMemo, useState } from 'react';
import HeroBanner from '@/components/homepage/HeroBanner';
import ValueProps from '@/components/homepage/ValueProps';
import { MedusaCartProvider } from '@/hooks/useMedusaCart';
import LatestDeals from '@/components/homepage/LatestDeals';
import DealCollection from '@/components/homepage/DealCollection';
import {
  useMedusaBackendCollections,
  useMedusaBackendProducts,
} from '@/hooks/useMedusaBackendProducts';
import { useCollectionStore } from '@/stores/collectionStore';
import { useCategoryStore } from '@/stores/categoriesStore';
import { useStoreConfigStore } from '@/stores/storeConfigStore';
import { useStoreTheme } from '@/contexts/StoreThemeContext';
import ColorPaletteIndicator from '@/components/store/ColorPaletteIndicator';
import { useQuery } from '@tanstack/react-query';

interface StoreHomepageProps {
  storeHandle: string;
  storeConfig?: any;
}

const StoreHomepage: React.FC<StoreHomepageProps> = ({
  storeHandle,
  storeConfig,
}) => {
  const [pageKey, setPageKey] = useState(0);
  const [productsByCollection, setProductsByCollection] = useState([]);
  const [loading, setLoading] = useState(true);
  const { error, fetchProducts } = useMedusaBackendProducts();
  const { fetchCollections, collections } = useMedusaBackendCollections();

  const {
    storeName,
    // storeHandle,
    storeLogo,
    storeTheme,
    storeAddress,
    storeEmail,
    storeConatctNumber,
    storeDescription,
  } = useStoreConfigStore();

  // Get theme context for dynamic styling
  const { currentTheme } = useStoreTheme();

  const setSelectedProductId = useCategoryStore(
    state => state.setSelectedProductId
  );

  const setCollectionData = useCollectionStore(
    state => state.setCollectionData
  );

  const fetchDealProducts = async () => {
    try {
      await fetchCollections();
    } catch (error) {}
  };

  const handleViewAllCollection = async (collectionId: string) => {
    const collectionData = collections.find(
      collection => collection.id === collectionId
    );
    const products = await fetchProducts({
      collection_id: [collectionId],
      limit: 10000,
    });
    const sampleCollection = {
      collection_id: collectionData?.id,
      collection_name: collectionData?.title,
      collection_slug: collectionData?.handle,
      collection_products: products || [],
    };

    setCollectionData([sampleCollection]);
  };

  const fetchCollectionProducts = async () => {
    setLoading(true);
    const results = await Promise.all(
      collections.map(async data => {
        const products = await fetchProducts({
          collection_id: [data.id],
          limit: 8,
          // fields: [
          //   'id',
          //   'title',
          //   'created_at',
          //   'updated_at',
          //   'metadata',
          //   'subtitle',
          //   'description',
          //   'handle',
          //   'thumbnail',
          //   'collection_id',
          //   'tags',
          //   'variants',
          //   'image',
          // ],
        });
        console.log('=========products::::::::', data.title, data.id, products);

        if (!products || products.length === 0) return null; // Skip empty collections
        return {
          collectionId: data.id,
          collectionName: data.title,
          products,
          slug: data.handle,
        };
      })
    );

    // Filter out null results (i.e., collections with no products)
    const filteredResults = results.filter(Boolean);
    setProductsByCollection(filteredResults);
    setLoading(false);
  };

  const handleProductClick = (productId: string) => {
    try {
      console.log('productId::::::::', productId);
      setSelectedProductId(productId);
    } catch (error) {
      console.error('Error:::::', error);
    }
  };

  useEffect(() => {
    if (collections.length < 1) return;
    fetchCollectionProducts();
  }, [collections]);

  useEffect(() => {
    // Force re-render when component mounts
    setPageKey(prev => prev + 1);
  }, []);

  // Listen for route changes and refresh banner
  useEffect(() => {
    const handleRouteChange = () => {
      setPageKey(prev => prev + 1);
    };

    // Listen for browser navigation events
    window.addEventListener('popstate', handleRouteChange);

    return () => {
      window.removeEventListener('popstate', handleRouteChange);
    };
  }, []);

  useEffect(() => {
    fetchDealProducts();
  }, []);

  return (
    <div
      className='min-h-screen mb-8'
      style={{ backgroundColor: 'var(--store-background)' }}
    >
      {/* Color palette indicator for development */}
      {process.env.NODE_ENV === 'development' && (
        <div className='fixed top-4 right-4 z-50 space-y-2'>
          <div className='store-card p-2 text-xs'>
            <div className='flex items-center gap-2'>
              <div className='w-3 h-3 rounded-full bg-store-primary' />
              <span className='store-text-main'>
                Theme:{' '}
                {currentTheme === 'palette' ? 'Color Palette' : 'Default'}
              </span>
            </div>
          </div>
          <ColorPaletteIndicator showDetails={true} />
        </div>
      )}

      {/* Hero Banner with Carousel Component */}
      <HeroBanner key={`hero-banner-${pageKey}`} storeHandle={storeHandle} />

      <LatestDeals
        title='New arrivals'
        subtitle='Check out our newest products'
        maxProducts={8}
        productType='New arrivals'
        showViewAll={false}
        viewAllLink={`/${storeHandle}/deals?type=new-arrivals`}
        handleCardClick={handleProductClick}
      />
      <LatestDeals
        title='Top Selling'
        subtitle='Handpicked products just for you'
        maxProducts={8}
        productType='latest'
        showViewAll={false}
        viewAllLink={`/${storeHandle}/products`}
        handleCardClick={handleProductClick}
      />
      {console.log(
        '===========productsByCollection===========',
        productsByCollection
      )}
      {/* Collection-based Product Sections */}
      {productsByCollection.length > 0 &&
        productsByCollection.map((collection, index) => (
          <DealCollection
            data={collection}
            key={index}
            loading={loading}
            handleClick={handleViewAllCollection}
            storeHandle={storeHandle}
          />
        ))}

      {/* Theme Selector for Development/Testing - Will be implemented later */}
    </div>
  );
};

export default StoreHomepage;
