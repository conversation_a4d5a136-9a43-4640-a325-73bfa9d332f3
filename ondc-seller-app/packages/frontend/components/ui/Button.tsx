'use client';

import React from 'react';
import { cn } from '@/lib/utils';

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?:
    | 'primary'
    | 'secondary'
    | 'outline'
    | 'ghost'
    | 'destructive'
    | 'store-primary'
    | 'store-secondary'
    | 'store-outline'
    | 'store-ghost'
    | 'store-accent'
    | 'store-success'
    | 'store-warning'
    | 'store-error';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  loading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant = 'primary',
      size = 'md',
      loading = false,
      leftIcon,
      rightIcon,
      children,
      disabled,
      ...props
    },
    ref
  ) => {
    // Check if it's a store variant
    const isStoreVariant = variant.startsWith('store-');

    const baseClasses = cn(
      'inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none disabled:cursor-not-allowed',
      // Apply different focus styles for store vs regular variants
      isStoreVariant
        ? 'focus:ring-2 focus:ring-offset-2 disabled:opacity-50'
        : 'focus:ring-2 focus:ring-offset-2 disabled:opacity-50'
    );

    const variantClasses = {
      primary:
        'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-sm hover:shadow-md rounded-md',
      secondary:
        'bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500 border border-gray-300 rounded-md',
      outline:
        'border border-blue-600 text-blue-600 hover:bg-blue-50 focus:ring-blue-500 bg-transparent rounded-md',
      ghost:
        'text-gray-700 hover:bg-gray-100 focus:ring-gray-500 bg-transparent rounded-md',
      destructive:
        'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 shadow-sm hover:shadow-md rounded-md',
      'store-primary': 'store-btn-primary store-focus-ring',
      'store-secondary': 'store-btn-secondary store-focus-ring',
      'store-outline': 'store-btn-outline store-focus-ring',
      'store-ghost': 'store-btn-ghost store-focus-ring',
      'store-accent':
        'bg-store-accent text-store-background hover:bg-store-accent-dark focus:ring-store-accent store-focus-ring rounded-md shadow-store hover:shadow-store-lg transition-all duration-200',
      'store-success':
        'bg-store-success text-white hover:bg-store-success-dark focus:ring-store-success store-focus-ring rounded-md shadow-sm hover:shadow-md transition-all duration-200',
      'store-warning':
        'bg-store-warning text-white hover:bg-store-warning-dark focus:ring-store-warning store-focus-ring rounded-md shadow-sm hover:shadow-md transition-all duration-200',
      'store-error':
        'bg-store-error text-white hover:bg-store-error-dark focus:ring-store-error store-focus-ring rounded-md shadow-sm hover:shadow-md transition-all duration-200',
    };

    const sizeClasses = {
      sm: 'px-3 py-1.5 text-sm h-8',
      md: 'px-4 py-2 text-sm h-10',
      lg: 'px-6 py-3 text-base h-12',
      xl: 'px-8 py-4 text-lg h-14',
    };

    const isDisabled = disabled || loading;

    return (
      <button
        ref={ref}
        className={cn(
          baseClasses,
          variantClasses[variant],
          sizeClasses[size],
          className
        )}
        disabled={isDisabled}
        {...props}
      >
        {loading && (
          <div
            className={cn(
              'animate-spin -ml-1 mr-2 h-4 w-4 rounded-full border-2',
              isStoreVariant
                ? 'store-spinner'
                : 'border-gray-300 border-t-current'
            )}
          />
        )}
        {!loading && leftIcon && <span className='mr-2'>{leftIcon}</span>}
        {children}
        {!loading && rightIcon && <span className='ml-2'>{rightIcon}</span>}
      </button>
    );
  }
);

Button.displayName = 'Button';

export default Button;
