'use client';

import React, { useState, useCallback, useMemo } from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { formatCurrency } from '@/lib/utils/product-utils';
import Button from './Button';
import Badge from './Badge';
import {
  StarIcon,
  HeartIcon,
  ShoppingCartIcon,
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid';
import { FEATURE_FLAGS } from '@/lib/config/featureFlags';

export interface ProductCardProps {
  id: string;
  title: string;
  description?: string;
  price: number;
  originalPrice?: number;
  currency?: string;
  image?: string;
  images?: string[];
  category?: string;
  brand?: string;
  rating?: number;
  reviewCount?: number;
  inStock?: boolean;
  isNew?: boolean;
  isFeatured?: boolean;
  tags?: string[];
  variant?: 'default' | 'compact' | 'detailed';
  showQuickAdd?: boolean;
  showWishlist?: boolean;
  showCompare?: boolean;
  onQuickAdd?: (productId: string) => void;
  onWishlistToggle?: (productId: string) => void;
  onCompareToggle?: (productId: string) => void;
  className?: string;
  imageClassName?: string;
  contentClassName?: string;
}

/**
 * Optimized ProductCard Component with React.memo and performance optimizations
 * - Memoized to prevent unnecessary re-renders
 * - Memoized event handlers and computed values
 * - Performance monitoring integration
 */
const OptimizedProductCard = React.memo<ProductCardProps>(
  ({
    id,
    title,
    description,
    price,
    originalPrice,
    currency = 'USD',
    image,
    images,
    category,
    brand,
    rating,
    reviewCount,
    inStock = true,
    isNew = false,
    isFeatured = false,
    tags = [],
    variant = 'default',
    showQuickAdd = true,
    showWishlist = true,
    showCompare = false,
    onQuickAdd,
    onWishlistToggle,
    onCompareToggle,
    className,
    imageClassName,
    contentClassName,
  }) => {
    const [isWishlisted, setIsWishlisted] = useState(false);
    const [isCompared, setIsCompared] = useState(false);
    const [imageLoading, setImageLoading] = useState(true);

    // ✅ OPTIMIZATION: Memoized computed values
    const hasDiscount = useMemo(
      () => originalPrice && originalPrice > price,
      [originalPrice, price]
    );

    const discountPercentage = useMemo(
      () =>
        hasDiscount
          ? Math.round(((originalPrice! - price) / originalPrice!) * 100)
          : 0,
      [hasDiscount, originalPrice, price]
    );

    const productImage = useMemo(
      () => image || images?.[0] || '/images/products/placeholder.svg',
      [image, images]
    );

    // Use the existing formatCurrency utility from imports

    // ✅ OPTIMIZATION: Memoized event handlers
    const handleQuickAdd = useCallback(() => {
      if (onQuickAdd && inStock) {
        const startTime = performance.now();
        onQuickAdd(id);

        if (
          process.env.NODE_ENV === 'development' &&
          FEATURE_FLAGS.ENABLE_PERFORMANCE_MONITORING
        ) {
          const duration = performance.now() - startTime;
          console.log(
            `🛒 Quick add action completed in ${duration.toFixed(2)}ms`
          );
        }
      }
    }, [onQuickAdd, id, inStock]);

    const handleWishlistToggle = useCallback(() => {
      setIsWishlisted(prev => !prev);
      onWishlistToggle?.(id);
    }, [onWishlistToggle, id]);

    const handleCompareToggle = useCallback(() => {
      setIsCompared(prev => !prev);
      onCompareToggle?.(id);
    }, [onCompareToggle, id]);

    const handleImageLoad = useCallback(() => {
      setImageLoading(false);
    }, []);

    // ✅ OPTIMIZATION: Memoized render functions
    const renderBadges = useMemo(() => {
      const badges = [];

      if (isNew) {
        badges.push(
          <Badge
            key='new'
            variant='secondary'
            className='absolute top-2 left-2 bg-green-500 text-white'
          >
            New
          </Badge>
        );
      }

      if (isFeatured) {
        badges.push(
          <Badge
            key='featured'
            variant='secondary'
            className='absolute top-2 right-2 bg-blue-500 text-white'
          >
            Featured
          </Badge>
        );
      }

      if (hasDiscount) {
        badges.push(
          <Badge
            key='discount'
            variant='destructive'
            className='absolute top-8 left-2'
          >
            -{discountPercentage}%
          </Badge>
        );
      }

      return badges;
    }, [isNew, isFeatured, hasDiscount, discountPercentage]);

    const renderActionButtons = useMemo(
      () => (
        <div className='absolute top-2 right-2 flex flex-col gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200'>
          {showWishlist && (
            <Button
              size='sm'
              variant='store-ghost'
              className='h-8 w-8 p-0 bg-store-background/90 hover:bg-store-background shadow-store'
              onClick={handleWishlistToggle}
            >
              {isWishlisted ? (
                <HeartIconSolid className='h-4 w-4 text-store-error' />
              ) : (
                <HeartIcon className='h-4 w-4' />
              )}
            </Button>
          )}

          {showCompare && (
            <Button
              size='sm'
              variant='store-ghost'
              className='h-8 w-8 p-0 bg-store-background/90 hover:bg-store-background shadow-store'
              onClick={handleCompareToggle}
            >
              <ShoppingCartIcon className='h-4 w-4' />
            </Button>
          )}
        </div>
      ),
      [
        showWishlist,
        showCompare,
        isWishlisted,
        handleWishlistToggle,
        handleCompareToggle,
      ]
    );

    const renderRating = useMemo(() => {
      if (!rating) return null;

      return (
        <div className='flex items-center gap-1 mt-1'>
          <div className='flex'>
            {[1, 2, 3, 4, 5].map(star => (
              <StarIcon
                key={star}
                className={cn(
                  'h-3 w-3',
                  star <= rating
                    ? 'text-store-warning fill-current'
                    : 'text-store-text-lighter'
                )}
              />
            ))}
          </div>
          {reviewCount && (
            <span className='text-xs text-store-text-light'>
              ({reviewCount})
            </span>
          )}
        </div>
      );
    }, [rating, reviewCount]);

    const renderQuickAddButton = useMemo(() => {
      if (!showQuickAdd || !inStock) return null;

      return (
        <div className='absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center'>
          <Button
            onClick={handleQuickAdd}
            variant='default'
            size='sm'
            className='transform translate-y-2 group-hover:translate-y-0 transition-transform duration-200'
          >
            Quick Add
          </Button>
        </div>
      );
    }, [showQuickAdd, inStock, handleQuickAdd]);

    // ✅ OPTIMIZATION: Memoized CSS classes
    const cardClasses = useMemo(
      () =>
        cn(
          'group relative store-card overflow-hidden transition-all duration-200',
          !inStock && 'opacity-75',
          variant === 'compact' && 'max-w-xs',
          className
        ),
      [inStock, variant, className]
    );

    const imageContainerClasses = useMemo(
      () =>
        cn(
          'relative overflow-hidden bg-store-surface',
          variant === 'compact' ? 'aspect-square' : 'aspect-[4/3]',
          imageClassName
        ),
      [variant, imageClassName]
    );

    const contentClasses = useMemo(
      () => cn('p-3 flex-1 flex flex-col', contentClassName),
      [contentClassName]
    );

    return (
      <Link href={`/products/${id}`} className={cardClasses}>
        {/* Product Image */}
        <div className={imageContainerClasses}>
          <img
            src={productImage}
            alt={title}
            className='object-cover group-hover:scale-105 transition-transform duration-300 w-full h-full'
            sizes='(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw'
            onLoad={handleImageLoad}
            loading='lazy'
          />

          {/* Badges */}
          {renderBadges}

          {/* Action Buttons */}
          {renderActionButtons}

          {/* Quick Add Overlay */}
          {renderQuickAddButton}

          {/* Out of Stock Overlay */}
          {!inStock && (
            <div className='absolute inset-0 bg-store-disabled flex items-center justify-center'>
              <span className='text-store-background font-medium text-sm bg-store-text/75 px-3 py-1 rounded store-badge'>
                Out of Stock
              </span>
            </div>
          )}
        </div>

        {/* Product Content */}
        <div className={contentClasses}>
          {/* Category & Brand */}
          {(category || brand) && (
            <div className='flex items-center gap-2 mb-1'>
              {category && (
                <span className='text-xs text-store-text-light uppercase tracking-wide'>
                  {category}
                </span>
              )}
              {brand && category && (
                <span className='text-store-text-lighter'>•</span>
              )}
              {brand && (
                <span className='text-xs text-store-text-light'>{brand}</span>
              )}
            </div>
          )}

          {/* Title */}
          <h3
            className={cn(
              'font-medium store-text-primary line-clamp-2 mb-1',
              variant === 'compact' ? 'text-sm' : 'text-base'
            )}
          >
            {title}
          </h3>

          {/* Description (only for detailed variant) */}
          {variant === 'detailed' && description && (
            <p className='text-sm store-text-light line-clamp-2 mb-2'>
              {description}
            </p>
          )}

          {/* Rating */}
          {renderRating}

          {/* Price */}
          <div className='flex items-center space-x-2 mt-2'>
            <span
              className={cn(
                'font-semibold store-text-primary',
                variant === 'compact' ? 'text-base' : 'text-lg'
              )}
            >
              {formatCurrency(price, currency)}
            </span>
            {hasDiscount && (
              <span className='text-sm store-text-light line-through'>
                {formatCurrency(originalPrice!, currency)}
              </span>
            )}
          </div>

          {/* Tags (only for detailed variant) */}
          {variant === 'detailed' && tags.length > 0 && (
            <div className='mt-2 flex flex-wrap gap-1'>
              {tags.slice(0, 3).map((tag, index) => (
                <span
                  key={index}
                  className='store-badge-secondary text-xs px-2 py-1 rounded'
                >
                  {tag}
                </span>
              ))}
            </div>
          )}
        </div>
      </Link>
    );
  }
);

OptimizedProductCard.displayName = 'OptimizedProductCard';

export default OptimizedProductCard;
