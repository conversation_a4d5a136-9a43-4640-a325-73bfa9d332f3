'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { cn } from '@/lib/utils';
import ProductCard, { ProductCardProps } from './ProductCard';
import { SkeletonProduct } from './Skeleton';
import Button from './Button';

export interface Product {
  id: string;
  title: string;
  description?: string;
  price: number;
  originalPrice?: number;
  currency?: string;
  image?: string;
  images?: string[];
  category?: string;
  brand?: string;
  rating?: number;
  reviewCount?: number;
  inStock?: boolean;
  isNew?: boolean;
  isFeatured?: boolean;
  tags?: string[];
}

export interface ProductGridProps {
  products: Product[];
  loading?: boolean;
  error?: string | null;
  hasMore?: boolean;
  onLoadMore?: () => void;
  onQuickAdd?: (productId: string) => void;
  onWishlistToggle?: (productId: string) => void;
  onCompareToggle?: (productId: string) => void;
  columns?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
    wide?: number;
  };
  gap?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'compact' | 'detailed';
  showQuickAdd?: boolean;
  showWishlist?: boolean;
  showCompare?: boolean;
  infiniteScroll?: boolean;
  loadMoreThreshold?: number;
  className?: string;
  emptyStateTitle?: string;
  emptyStateDescription?: string;
  emptyStateAction?: React.ReactNode;
}

const ProductGrid: React.FC<ProductGridProps> = ({
  products,
  loading = false,
  error = null,
  hasMore = false,
  onLoadMore,
  onQuickAdd,
  onWishlistToggle,
  onCompareToggle,
  columns = {
    mobile: 1,
    tablet: 2,
    desktop: 3,
    wide: 4,
  },
  gap = 'md',
  variant = 'default',
  showQuickAdd = true,
  showWishlist = true,
  showCompare = false,
  infiniteScroll = false,
  loadMoreThreshold = 200,
  className,
  emptyStateTitle = 'No products found',
  emptyStateDescription = 'Try adjusting your search or filter criteria.',
  emptyStateAction,
}) => {
  const [loadingMore, setLoadingMore] = useState(false);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadMoreRef = useRef<HTMLDivElement | null>(null);

  // Infinite scroll implementation
  const handleLoadMore = useCallback(async () => {
    if (loadingMore || !hasMore || !onLoadMore) return;

    setLoadingMore(true);
    try {
      await onLoadMore();
    } finally {
      setLoadingMore(false);
    }
  }, [loadingMore, hasMore, onLoadMore]);

  useEffect(() => {
    if (!infiniteScroll || !hasMore || !onLoadMore) return;

    const currentRef = loadMoreRef.current;
    if (!currentRef) return;

    observerRef.current = new IntersectionObserver(
      entries => {
        const [entry] = entries;
        if (entry.isIntersecting) {
          handleLoadMore();
        }
      },
      {
        rootMargin: `${loadMoreThreshold}px`,
      }
    );

    observerRef.current.observe(currentRef);

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [infiniteScroll, hasMore, onLoadMore, handleLoadMore, loadMoreThreshold]);

  const getGridClasses = () => {
    const baseClasses = 'grid';
    const gapClasses = {
      sm: 'gap-3',
      md: 'gap-4 md:gap-6',
      lg: 'gap-6 md:gap-8',
    };

    const columnClasses = [
      `grid-cols-${columns.mobile || 1}`,
      columns.tablet && `sm:grid-cols-${columns.tablet}`,
      columns.desktop && `lg:grid-cols-${columns.desktop}`,
      columns.wide && `xl:grid-cols-${columns.wide}`,
    ]
      .filter(Boolean)
      .join(' ');

    return cn(baseClasses, gapClasses[gap], columnClasses);
  };

  const renderSkeletonGrid = (count: number = 12) => {
    return (
      <div className={getGridClasses()}>
        {Array.from({ length: count }).map((_, index) => (
          <SkeletonProduct key={index} />
        ))}
      </div>
    );
  };

  const renderEmptyState = () => (
    <div className='text-center py-12'>
      <div className='w-16 h-16 bg-store-surface rounded-full flex items-center justify-center mx-auto mb-4 shadow-store'>
        <svg
          className='w-8 h-8 text-store-text-light'
          fill='none'
          stroke='currentColor'
          viewBox='0 0 24 24'
        >
          <path
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth={2}
            d='M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4'
          />
        </svg>
      </div>
      <h3 className='text-lg font-medium text-store-text mb-2'>
        {emptyStateTitle}
      </h3>
      <p className='text-store-text-light mb-4 max-w-md mx-auto'>
        {emptyStateDescription}
      </p>
      {emptyStateAction}
    </div>
  );

  const renderErrorState = () => (
    <div className='text-center py-12'>
      <div className='w-16 h-16 bg-store-error/10 rounded-full flex items-center justify-center mx-auto mb-4 shadow-store'>
        <svg
          className='w-8 h-8 text-store-error'
          fill='none'
          stroke='currentColor'
          viewBox='0 0 24 24'
        >
          <path
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth={2}
            d='M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
          />
        </svg>
      </div>
      <h3 className='text-lg font-medium text-store-text mb-2'>
        Something went wrong
      </h3>
      <p className='text-store-text-light mb-4 max-w-md mx-auto'>
        {error || 'There was an error loading the products. Please try again.'}
      </p>
      <Button variant='store-outline' onClick={() => window.location.reload()}>
        Try Again
      </Button>
    </div>
  );

  const renderLoadMoreButton = () => {
    if (!hasMore || infiniteScroll) return null;

    return (
      <div className='text-center mt-8'>
        <Button
          variant='store-outline'
          size='lg'
          onClick={handleLoadMore}
          disabled={loadingMore}
          className='min-w-[200px]'
        >
          {loadingMore ? (
            <>
              <svg
                className='animate-spin -ml-1 mr-3 h-4 w-4'
                fill='none'
                viewBox='0 0 24 24'
              >
                <circle
                  className='opacity-25'
                  cx='12'
                  cy='12'
                  r='10'
                  stroke='currentColor'
                  strokeWidth='4'
                />
                <path
                  className='opacity-75'
                  fill='currentColor'
                  d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'
                />
              </svg>
              Loading...
            </>
          ) : (
            'Load More Products'
          )}
        </Button>
      </div>
    );
  };

  const renderInfiniteScrollTrigger = () => {
    if (!infiniteScroll || !hasMore) return null;

    return (
      <div ref={loadMoreRef} className='text-center py-8'>
        {loadingMore && (
          <div className='flex items-center justify-center'>
            <svg
              className='animate-spin h-6 w-6 text-gray-400'
              fill='none'
              viewBox='0 0 24 24'
            >
              <circle
                className='opacity-25'
                cx='12'
                cy='12'
                r='10'
                stroke='currentColor'
                strokeWidth='4'
              />
              <path
                className='opacity-75'
                fill='currentColor'
                d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'
              />
            </svg>
            <span className='ml-2 text-gray-500'>Loading more products...</span>
          </div>
        )}
      </div>
    );
  };

  // Handle error state
  if (error && !loading && products.length === 0) {
    return <div className={className}>{renderErrorState()}</div>;
  }

  // Handle initial loading state
  if (loading && products.length === 0) {
    return <div className={className}>{renderSkeletonGrid()}</div>;
  }

  // Handle empty state
  if (!loading && products.length === 0) {
    return <div className={className}>{renderEmptyState()}</div>;
  }

  return (
    <div className={className}>
      {/* Product Grid */}
      <div className={getGridClasses()}>
        {products.map(product => (
          <ProductCard
            key={product.id}
            {...product}
            variant={variant}
            showQuickAdd={showQuickAdd}
            showWishlist={showWishlist}
            showCompare={showCompare}
            onQuickAdd={onQuickAdd}
            onWishlistToggle={onWishlistToggle}
            onCompareToggle={onCompareToggle}
          />
        ))}
      </div>

      {/* Loading More Skeletons */}
      {loading && products.length > 0 && (
        <div className={cn(getGridClasses(), 'mt-6')}>
          {Array.from({ length: 4 }).map((_, index) => (
            <SkeletonProduct key={`loading-${index}`} />
          ))}
        </div>
      )}

      {/* Load More Button */}
      {renderLoadMoreButton()}

      {/* Infinite Scroll Trigger */}
      {renderInfiniteScrollTrigger()}
    </div>
  );
};

export default ProductGrid;
