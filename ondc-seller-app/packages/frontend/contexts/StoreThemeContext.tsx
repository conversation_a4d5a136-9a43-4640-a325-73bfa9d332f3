'use client';

import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { useStoreConfigStore } from '@/stores/storeConfigStore';
import { generateThemeColors } from '@/lib/utils/colorPaletteExtractor';

// Legacy theme system - DEPRECATED
// Now using color palette-based theming only
export type StoreThemeValue = 'palette' | 'default';

// Default CSS variables for fallback when no color palette is available
const DEFAULT_THEME_VARIABLES = {
  '--store-primary': '#3B82F6',
  '--store-primary-rgb': '59, 130, 246',
  '--store-primary-light': '#60A5FA',
  '--store-primary-dark': '#2563EB',
  '--store-secondary': '#F1F5F9',
  '--store-secondary-rgb': '241, 245, 249',
  '--store-accent': '#0EA5E9',
  '--store-accent-rgb': '14, 165, 233',
  '--store-background': '#FFFFFF',
  '--store-background-rgb': '255, 255, 255',
  '--store-surface': '#F8FAFC',
  '--store-surface-rgb': '248, 250, 252',
  '--store-text': '#1E293B',
  '--store-text-rgb': '30, 41, 59',
  '--store-text-light': '#64748B',
  '--store-text-light-rgb': '100, 116, 139',
  '--store-border': '#E2E8F0',
  '--store-border-rgb': '226, 232, 240',
  '--store-shadow':
    '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
  '--store-shadow-lg':
    '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
} as const;

interface StoreThemeContextType {
  currentTheme: StoreThemeValue;
  applyColorPalette: () => void;
  applyDefaultTheme: () => void;
}

const StoreThemeContext = createContext<StoreThemeContextType | undefined>(
  undefined
);

interface StoreThemeProviderProps {
  children: ReactNode;
}

export const StoreThemeProvider = ({ children }: StoreThemeProviderProps) => {
  const { storeColorPalette } = useStoreConfigStore();

  // Apply default theme CSS variables to document root
  const applyDefaultTheme = () => {
    if (typeof document === 'undefined') return;

    // Apply default CSS variables to document root
    Object.entries(DEFAULT_THEME_VARIABLES).forEach(([property, value]) => {
      document.documentElement.style.setProperty(property, value);
    });

    // Set data attribute for default styling
    document.documentElement.setAttribute('data-store-theme', 'default');

    console.log('🎨 [StoreTheme] Applied default theme');
  };

  // Remove theme CSS variables from document root
  const removeTheme = () => {
    if (typeof document === 'undefined') return;

    // Remove all store theme CSS variables
    const themeVariables = Object.keys(DEFAULT_THEME_VARIABLES);
    themeVariables.forEach(property => {
      document.documentElement.style.removeProperty(property);
    });

    // Remove data attributes
    document.documentElement.removeAttribute('data-store-theme');
    document.documentElement.removeAttribute('data-store-palette');

    console.log('🎨 [StoreTheme] Removed theme variables');
  };

  // Apply color palette from store configuration
  const applyColorPalette = () => {
    if (typeof document === 'undefined') return;

    // Check if we have a valid color palette
    if (!storeColorPalette || !storeColorPalette.vibrant) {
      console.log(
        '🎨 [StoreTheme] No color palette available, using default theme'
      );
      return;
    }

    console.log('🎨 [StoreTheme] Applying color palette:', storeColorPalette);

    // Helper function to convert hex to RGB
    const hexToRgb = (hex: string): string => {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
      return result
        ? `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(
            result[3],
            16
          )}`
        : '0, 0, 0';
    };

    // Helper function to create lighter/darker variants
    const adjustBrightness = (hex: string, percent: number): string => {
      const num = parseInt(hex.replace('#', ''), 16);
      const amt = Math.round(2.55 * percent);
      const R = (num >> 16) + amt;
      const G = ((num >> 8) & 0x00ff) + amt;
      const B = (num & 0x0000ff) + amt;
      return (
        '#' +
        (
          0x1000000 +
          (R < 255 ? (R < 1 ? 0 : R) : 255) * 0x10000 +
          (G < 255 ? (G < 1 ? 0 : G) : 255) * 0x100 +
          (B < 255 ? (B < 1 ? 0 : B) : 255)
        )
          .toString(16)
          .slice(1)
      );
    };

    // Extract colors from palette with fallbacks
    const primaryColor = storeColorPalette.vibrant || '#3B82F6';
    const primaryLightColor =
      storeColorPalette.vibrantLight || adjustBrightness(primaryColor, 20);
    const primaryDarkColor =
      storeColorPalette.vibrantDark || adjustBrightness(primaryColor, -20);
    const accentColor = storeColorPalette.dominant || '#0EA5E9';
    const mutedColor = storeColorPalette.muted || '#64748B';
    const mutedDarkColor = storeColorPalette.mutedDark || '#1E293B';
    const mutedLightColor = storeColorPalette.mutedLight || '#F1F5F9';

    // Create comprehensive color variables
    const colorVariables = {
      // Primary colors (vibrant palette)
      '--store-primary': primaryColor,
      '--store-primary-rgb': hexToRgb(primaryColor),
      '--store-primary-light': primaryLightColor,
      '--store-primary-light-rgb': hexToRgb(primaryLightColor),
      '--store-primary-dark': primaryDarkColor,
      '--store-primary-dark-rgb': hexToRgb(primaryDarkColor),

      // Accent colors (dominant palette)
      '--store-accent': accentColor,
      '--store-accent-rgb': hexToRgb(accentColor),
      '--store-accent-light': adjustBrightness(accentColor, 15),
      '--store-accent-light-rgb': hexToRgb(adjustBrightness(accentColor, 15)),
      '--store-accent-dark': adjustBrightness(accentColor, -15),
      '--store-accent-dark-rgb': hexToRgb(adjustBrightness(accentColor, -15)),

      // Secondary/Surface colors (muted palette)
      '--store-secondary': mutedLightColor,
      '--store-secondary-rgb': hexToRgb(mutedLightColor),
      '--store-surface': adjustBrightness(mutedLightColor, 5),
      '--store-surface-rgb': hexToRgb(adjustBrightness(mutedLightColor, 5)),
      '--store-surface-hover': adjustBrightness(mutedLightColor, -5),
      '--store-surface-hover-rgb': hexToRgb(
        adjustBrightness(mutedLightColor, -5)
      ),

      // Text colors (muted palette)
      '--store-text': mutedDarkColor,
      '--store-text-rgb': hexToRgb(mutedDarkColor),
      '--store-text-light': mutedColor,
      '--store-text-light-rgb': hexToRgb(mutedColor),
      '--store-text-lighter': adjustBrightness(mutedColor, 20),
      '--store-text-lighter-rgb': hexToRgb(adjustBrightness(mutedColor, 20)),

      // Border colors
      '--store-border': adjustBrightness(mutedLightColor, -10),
      '--store-border-rgb': hexToRgb(adjustBrightness(mutedLightColor, -10)),
      '--store-border-light': adjustBrightness(mutedLightColor, -5),
      '--store-border-light-rgb': hexToRgb(
        adjustBrightness(mutedLightColor, -5)
      ),

      // Background colors (keep neutral for readability)
      '--store-background': '#FFFFFF',
      '--store-background-rgb': '255, 255, 255',
      '--store-background-alt': '#FAFAFA',
      '--store-background-alt-rgb': '250, 250, 250',

      // Success, Warning, Error colors (semantic)
      '--store-success': '#10B981',
      '--store-success-rgb': '16, 185, 129',
      '--store-success-light': '#34D399',
      '--store-success-dark': '#059669',
      '--store-warning': '#F59E0B',
      '--store-warning-rgb': '245, 158, 11',
      '--store-warning-light': '#FBBF24',
      '--store-warning-dark': '#D97706',
      '--store-error': '#EF4444',
      '--store-error-rgb': '239, 68, 68',
      '--store-error-light': '#F87171',
      '--store-error-dark': '#DC2626',

      // Interactive states
      '--store-hover': `rgb(${hexToRgb(primaryColor)} / 0.1)`,
      '--store-active': `rgb(${hexToRgb(primaryColor)} / 0.2)`,
      '--store-focus': `rgb(${hexToRgb(primaryColor)} / 0.2)`,
      '--store-disabled': `rgb(${hexToRgb(mutedColor)} / 0.5)`,

      // Dynamic shadows based on primary color
      '--store-shadow': `0 1px 3px 0 rgb(${hexToRgb(
        primaryColor
      )} / 0.1), 0 1px 2px -1px rgb(${hexToRgb(primaryColor)} / 0.1)`,
      '--store-shadow-md': `0 4px 6px -1px rgb(${hexToRgb(
        primaryColor
      )} / 0.1), 0 2px 4px -2px rgb(${hexToRgb(primaryColor)} / 0.1)`,
      '--store-shadow-lg': `0 10px 15px -3px rgb(${hexToRgb(
        primaryColor
      )} / 0.1), 0 4px 6px -4px rgb(${hexToRgb(primaryColor)} / 0.1)`,
      '--store-shadow-xl': `0 20px 25px -5px rgb(${hexToRgb(
        primaryColor
      )} / 0.1), 0 8px 10px -6px rgb(${hexToRgb(primaryColor)} / 0.1)`,

      // Gradient backgrounds
      '--store-gradient-primary': `linear-gradient(135deg, ${primaryColor} 0%, ${primaryDarkColor} 100%)`,
      '--store-gradient-accent': `linear-gradient(135deg, ${accentColor} 0%, ${adjustBrightness(
        accentColor,
        -15
      )} 100%)`,
      '--store-gradient-surface': `linear-gradient(135deg, ${mutedLightColor} 0%, ${adjustBrightness(
        mutedLightColor,
        -5
      )} 100%)`,
    };

    // Apply CSS variables to document root
    Object.entries(colorVariables).forEach(([property, value]) => {
      if (value) {
        // Only apply if value exists
        document.documentElement.style.setProperty(property, value);
      }
    });

    // Set data attribute for palette-based styling
    document.documentElement.setAttribute('data-store-palette', 'active');

    console.log(
      '🎨 [StoreTheme] Applied color palette variables:',
      colorVariables
    );
  };

  // Initialize with default theme on mount
  useEffect(() => {
    applyDefaultTheme();

    // Cleanup function to remove theme when provider unmounts
    return () => {
      removeTheme();
    };
  }, []);

  // Apply color palette when it changes
  useEffect(() => {
    if (storeColorPalette && storeColorPalette.vibrant) {
      // Apply color palette after a short delay to ensure theme is applied first
      const timer = setTimeout(() => {
        applyColorPalette();
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [storeColorPalette]);

  const contextValue: StoreThemeContextType = {
    currentTheme:
      storeColorPalette && storeColorPalette.vibrant ? 'palette' : 'default',
    applyColorPalette,
    applyDefaultTheme,
  };

  return (
    <StoreThemeContext.Provider value={contextValue}>
      {children}
    </StoreThemeContext.Provider>
  );
};

export const useStoreTheme = () => {
  const context = useContext(StoreThemeContext);
  if (context === undefined) {
    throw new Error('useStoreTheme must be used within a StoreThemeProvider');
  }
  return context;
};
