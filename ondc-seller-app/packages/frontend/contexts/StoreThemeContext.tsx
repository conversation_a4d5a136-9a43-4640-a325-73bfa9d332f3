'use client';

import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { useStoreConfigStore } from '@/stores/storeConfigStore';
import { generateThemeColors } from '@/lib/utils/colorPaletteExtractor';

// Legacy theme system - DEPRECATED
// Now using color palette-based theming only
export type StoreThemeValue = 'palette' | 'default';

// Default CSS variables for fallback when no color palette is available
const DEFAULT_THEME_VARIABLES = {
  '--store-primary': '#3B82F6',
  '--store-primary-rgb': '59, 130, 246',
  '--store-primary-light': '#60A5FA',
  '--store-primary-dark': '#2563EB',
  '--store-secondary': '#F1F5F9',
  '--store-secondary-rgb': '241, 245, 249',
  '--store-accent': '#0EA5E9',
  '--store-accent-rgb': '14, 165, 233',
  '--store-background': '#FFFFFF',
  '--store-background-rgb': '255, 255, 255',
  '--store-surface': '#F8FAFC',
  '--store-surface-rgb': '248, 250, 252',
  '--store-text': '#1E293B',
  '--store-text-rgb': '30, 41, 59',
  '--store-text-light': '#64748B',
  '--store-text-light-rgb': '100, 116, 139',
  '--store-border': '#E2E8F0',
  '--store-border-rgb': '226, 232, 240',
  '--store-shadow':
    '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
  '--store-shadow-lg':
    '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
} as const;

interface StoreThemeContextType {
  currentTheme: StoreThemeValue;
  applyColorPalette: () => void;
  applyDefaultTheme: () => void;
}

const StoreThemeContext = createContext<StoreThemeContextType | undefined>(
  undefined
);

interface StoreThemeProviderProps {
  children: ReactNode;
}

export const StoreThemeProvider = ({ children }: StoreThemeProviderProps) => {
  const { storeColorPalette } = useStoreConfigStore();

  // Apply default theme CSS variables to document root
  const applyDefaultTheme = () => {
    if (typeof document === 'undefined') return;

    // Apply default CSS variables to document root
    Object.entries(DEFAULT_THEME_VARIABLES).forEach(([property, value]) => {
      document.documentElement.style.setProperty(property, value);
    });

    // Set data attribute for default styling
    document.documentElement.setAttribute('data-store-theme', 'default');

    console.log('🎨 [StoreTheme] Applied default theme');
  };

  // Remove theme CSS variables from document root
  const removeTheme = () => {
    if (typeof document === 'undefined') return;

    // Remove all store theme CSS variables
    const themeVariables = Object.keys(DEFAULT_THEME_VARIABLES);
    themeVariables.forEach(property => {
      document.documentElement.style.removeProperty(property);
    });

    // Remove data attributes
    document.documentElement.removeAttribute('data-store-theme');
    document.documentElement.removeAttribute('data-store-palette');

    console.log('🎨 [StoreTheme] Removed theme variables');
  };

  // Apply color palette from store configuration
  const applyColorPalette = () => {
    if (typeof document === 'undefined') return;

    // Check if we have a valid color palette
    if (!storeColorPalette || !storeColorPalette.vibrant) {
      console.log(
        '🎨 [StoreTheme] No color palette available, using default theme'
      );
      return;
    }

    console.log('🎨 [StoreTheme] Applying color palette:', storeColorPalette);

    // Helper function to convert hex to RGB
    const hexToRgb = (hex: string): string => {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
      return result
        ? `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(
            result[3],
            16
          )}`
        : '0, 0, 0';
    };

    // Apply the color palette to CSS variables
    const primaryColor = storeColorPalette.vibrant || '#3B82F6';
    const primaryLightColor = storeColorPalette.vibrantLight || '#60A5FA';
    const primaryDarkColor = storeColorPalette.vibrantDark || '#2563EB';
    const accentColor = storeColorPalette.dominant || '#0EA5E9';
    const secondaryColor = storeColorPalette.mutedLight || '#F1F5F9';
    const textColor = storeColorPalette.mutedDark || '#1E293B';
    const textLightColor = storeColorPalette.muted || '#64748B';
    const borderColor = storeColorPalette.mutedLight || '#E2E8F0';

    const colorVariables = {
      '--store-primary': primaryColor,
      '--store-primary-rgb': hexToRgb(primaryColor),
      '--store-primary-light': primaryLightColor,
      '--store-primary-dark': primaryDarkColor,
      '--store-accent': accentColor,
      '--store-accent-rgb': hexToRgb(accentColor),
      '--store-secondary': secondaryColor,
      '--store-secondary-rgb': hexToRgb(secondaryColor),
      '--store-text': textColor,
      '--store-text-rgb': hexToRgb(textColor),
      '--store-text-light': textLightColor,
      '--store-text-light-rgb': hexToRgb(textLightColor),
      '--store-border': borderColor,
      '--store-border-rgb': hexToRgb(borderColor),
      // Keep background colors neutral for readability
      '--store-background': '#FFFFFF',
      '--store-background-rgb': '255, 255, 255',
      '--store-surface': '#F8FAFC',
      '--store-surface-rgb': '248, 250, 252',
      // Dynamic shadows based on primary color
      '--store-shadow': `0 1px 3px 0 rgb(${hexToRgb(
        primaryColor
      )} / 0.1), 0 1px 2px -1px rgb(${hexToRgb(primaryColor)} / 0.1)`,
      '--store-shadow-lg': `0 10px 15px -3px rgb(${hexToRgb(
        primaryColor
      )} / 0.1), 0 4px 6px -4px rgb(${hexToRgb(primaryColor)} / 0.1)`,
    };

    // Apply CSS variables to document root
    Object.entries(colorVariables).forEach(([property, value]) => {
      if (value) {
        // Only apply if value exists
        document.documentElement.style.setProperty(property, value);
      }
    });

    // Set data attribute for palette-based styling
    document.documentElement.setAttribute('data-store-palette', 'active');

    console.log(
      '🎨 [StoreTheme] Applied color palette variables:',
      colorVariables
    );
  };

  // Initialize with default theme on mount
  useEffect(() => {
    applyDefaultTheme();

    // Cleanup function to remove theme when provider unmounts
    return () => {
      removeTheme();
    };
  }, []);

  // Apply color palette when it changes
  useEffect(() => {
    if (storeColorPalette && storeColorPalette.vibrant) {
      // Apply color palette after a short delay to ensure theme is applied first
      const timer = setTimeout(() => {
        applyColorPalette();
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [storeColorPalette]);

  const contextValue: StoreThemeContextType = {
    currentTheme:
      storeColorPalette && storeColorPalette.vibrant ? 'palette' : 'default',
    applyColorPalette,
    applyDefaultTheme,
  };

  return (
    <StoreThemeContext.Provider value={contextValue}>
      {children}
    </StoreThemeContext.Provider>
  );
};

export const useStoreTheme = () => {
  const context = useContext(StoreThemeContext);
  if (context === undefined) {
    // Return a safe default context for admin routes or components outside StoreThemeProvider
    console.warn(
      '⚠️ [StoreTheme] useStoreTheme used outside StoreThemeProvider, returning default context'
    );
    return {
      currentTheme: 'default' as StoreThemeValue,
      applyColorPalette: () => {
        console.warn(
          '⚠️ [StoreTheme] applyColorPalette called outside StoreThemeProvider'
        );
      },
      applyDefaultTheme: () => {
        console.warn(
          '⚠️ [StoreTheme] applyDefaultTheme called outside StoreThemeProvider'
        );
      },
    };
  }
  return context;
};
