'use client';

/**
 * Unified API Provider - Supports both SWR and TanStack Query
 * This allows gradual migration without breaking existing functionality
 */

import React from 'react';
import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { SWRConfig } from 'swr';
import { apiCallManager } from '@/lib/api/middleware/apiCallManager';
import { FEATURE_FLAGS, logFeatureFlags } from '@/lib/config/featureFlags';

interface UnifiedAPIProviderProps {
  children: React.ReactNode;
}

export function UnifiedAPIProvider({ children }: UnifiedAPIProviderProps) {
  // Log feature flags in development
  React.useEffect(() => {
    logFeatureFlags();
  }, []);

  const queryClient = apiCallManager.getQueryClient();

  // Enhanced SWR configuration for existing components
  const swrConfig = {
    revalidateOnFocus: false,
    revalidateOnReconnect: false,
    dedupingInterval: 10000, // Increased from default 2000ms
    errorRetryCount: 2,
    errorRetryInterval: 5000,
    onError: (error: any) => {
      if (process.env.NODE_ENV === 'development') {
        console.error('SWR Error:', error);
      }
    },
    onSuccess: (data: any, key: string) => {
      if (
        process.env.NODE_ENV === 'development' &&
        FEATURE_FLAGS.ENABLE_PERFORMANCE_MONITORING
      ) {
        console.log(`✅ SWR Success: ${key}`);
      }
    },
  };

  return (
    <QueryClientProvider client={queryClient}>
      {/* Keep SWR for components not yet migrated */}
      {/* <SWRConfig value={swrConfig}>
      </SWRConfig> */}
      {children}

      {/* TanStack Query DevTools - only in development */}
      {/* {process.env.NODE_ENV === 'development' && (
        <ReactQueryDevtools 
          initialIsOpen={false}
          position="bottom-right"
        />
      )} */}
    </QueryClientProvider>
  );
}

/**
 * Hook to get API performance metrics
 */
export function useAPIPerformance() {
  const [metrics, setMetrics] = React.useState<Record<string, any>>({});

  React.useEffect(() => {
    if (FEATURE_FLAGS.ENABLE_PERFORMANCE_MONITORING) {
      const interval = setInterval(() => {
        const report = apiCallManager.getPerformanceReport();
        setMetrics(report);
      }, 5000); // Update every 5 seconds

      return () => clearInterval(interval);
    }
  }, []);

  return {
    metrics,
    clearMetrics: () => apiCallManager.clearMetrics(),
  };
}

/**
 * Development component to display API performance
 */
export function APIPerformanceMonitor() {
  const { metrics } = useAPIPerformance();

  if (
    process.env.NODE_ENV !== 'development' ||
    !FEATURE_FLAGS.ENABLE_PERFORMANCE_MONITORING
  ) {
    return null;
  }

  return (
    <div
      style={{
        position: 'fixed',
        top: 10,
        right: 10,
        background: 'rgba(0,0,0,0.8)',
        color: 'white',
        padding: '10px',
        borderRadius: '5px',
        fontSize: '12px',
        zIndex: 9999,
        maxWidth: '300px',
      }}
    >
      <h4>API Performance</h4>
      {Object.entries(metrics).map(([endpoint, data]: [string, any]) => (
        <div key={endpoint} style={{ marginBottom: '5px' }}>
          <strong>{endpoint.split('/').pop()}</strong>
          <br />
          Avg: {data.avg}ms | Max: {data.max}ms | Calls: {data.count}
        </div>
      ))}
    </div>
  );
}
