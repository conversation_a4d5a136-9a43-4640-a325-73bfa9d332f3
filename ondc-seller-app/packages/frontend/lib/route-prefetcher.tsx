'use client';

import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useRef } from 'react';

// Common admin routes that should be prefetched
const ADMIN_ROUTES = [
  '/admin',
  '/admin/collections',
  '/admin/products',
  '/admin/orders',
  '/admin/customers',
  '/admin/analytics',
  '/admin/settings',
];

// Cache for prefetched routes
const prefetchCache = new Set<string>();

// Intersection observer for link prefetching
let intersectionObserver: IntersectionObserver | null = null;

// Route prefetching utility
export class RoutePrefetcher {
  private static instance: RoutePrefetcher;
  private prefetchedRoutes = new Set<string>();
  private router: any = null;

  static getInstance(): RoutePrefetcher {
    if (!RoutePrefetcher.instance) {
      RoutePrefetcher.instance = new RoutePrefetcher();
    }
    return RoutePrefetcher.instance;
  }

  setRouter(router: any) {
    this.router = router;
  }

  // Prefetch a single route
  prefetchRoute(route: string) {
    if (!this.router || this.prefetchedRoutes.has(route)) {
      return;
    }

    try {
      this.router.prefetch(route);
      this.prefetchedRoutes.add(route);
      console.log(`🚀 Prefetched route: ${route}`);
    } catch (error) {
      console.warn(`Failed to prefetch route ${route}:`, error);
    }
  }

  // Prefetch multiple routes
  prefetchRoutes(routes: string[]) {
    routes.forEach(route => this.prefetchRoute(route));
  }

  // Prefetch all admin routes
  prefetchAdminRoutes() {
    this.prefetchRoutes(ADMIN_ROUTES);
  }

  // Smart prefetching based on current route
  smartPrefetch(currentRoute: string) {
    const routePrefetchMap: Record<string, string[]> = {
      '/admin': ['/admin/collections', '/admin/products', '/admin/orders'],
      '/admin/collections': ['/admin/products', '/admin/collections/new'],
      '/admin/products': ['/admin/collections', '/admin/products/new'],
      '/admin/orders': ['/admin/customers', '/admin/products'],
    };

    const routesToPrefetch = routePrefetchMap[currentRoute] || [];
    this.prefetchRoutes(routesToPrefetch);
  }
}

// Hook for using route prefetching
export function useRoutePrefetcher() {
  const router = useRouter();
  const prefetcher = RoutePrefetcher.getInstance();

  useEffect(() => {
    prefetcher.setRouter(router);
  }, [router, prefetcher]);

  const prefetchRoute = useCallback(
    (route: string) => {
      prefetcher.prefetchRoute(route);
    },
    [prefetcher]
  );

  const prefetchRoutes = useCallback(
    (routes: string[]) => {
      prefetcher.prefetchRoutes(routes);
    },
    [prefetcher]
  );

  const prefetchAdminRoutes = useCallback(() => {
    prefetcher.prefetchAdminRoutes();
  }, [prefetcher]);

  const smartPrefetch = useCallback(
    (currentRoute: string) => {
      prefetcher.smartPrefetch(currentRoute);
    },
    [prefetcher]
  );

  return {
    prefetchRoute,
    prefetchRoutes,
    prefetchAdminRoutes,
    smartPrefetch,
  };
}

// Component for automatic route prefetching
export function RoutePrefetchProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const { prefetchAdminRoutes } = useRoutePrefetcher();

  useEffect(() => {
    // Prefetch admin routes after a short delay to avoid blocking initial render
    prefetchAdminRoutes();
  }, [prefetchAdminRoutes]);

  return <>{children}</>;
}

// Intersection Observer based prefetching for navigation links
export function useIntersectionPrefetch() {
  const { prefetchRoute } = useRoutePrefetcher();

  const observeLink = useCallback(
    (element: HTMLElement, href: string) => {
      if (!element || !href) return;

      const observer = new IntersectionObserver(
        entries => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              prefetchRoute(href);
              observer.unobserve(element);
            }
          });
        },
        {
          rootMargin: '50px',
          threshold: 0.1,
        }
      );

      observer.observe(element);

      return () => observer.unobserve(element);
    },
    [prefetchRoute]
  );

  return { observeLink };
}
