import { z } from 'zod';

// helper funtions
const emptyToUndefined = (v: unknown) => {
  if (v == null) return undefined;
  if (typeof v === 'string') {
    const t = v.trim();
    return t === '' ? undefined : t;
  }
  return v;
};

// Convenience for optional text fields
const optText = (schema: z.ZodString) =>
  z.preprocess(emptyToUndefined, schema.optional());

// Common validation patterns
const VALIDATION_PATTERNS = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^[+]?[\d\s\-\(\)]{10,15}$/,
  gst: /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/,
  pincode: /^[1-9][0-9]{5}$/,
  url: /^https?:\/\/.+\..+/,
  storeHandle: /^[a-z0-9\-]+$/,
};

// Business type options
export const BUSINESS_TYPES = [
  'individual',
  'partnership',
  'private_limited',
  'public_limited',
  'llp',
  'proprietorship',
] as const;

// Business category options
export const BUSINESS_CATEGORIES = [
  'fashion',
  'electronics',
  'home_garden',
  'health_beauty',
  'sports_outdoors',
  'books_media',
  'food_beverages',
  'automotive',
  'toys_games',
  'jewelry_accessories',
  'other',
] as const;

// Store theme options - DEPRECATED
// Now using color palette-based theming
export const STORE_THEMES = ['palette', 'default'] as const;

// Payment method schema
export const paymentMethodsSchema = z.object({
  cash_on_delivery: z.boolean().default(true),
  upi: z.boolean().default(true),
  credit_card: z.boolean().default(true),
  debit_card: z.boolean().default(true),
  net_banking: z.boolean().default(false),
  wallet: z.boolean().default(false),
  bnpl: z.boolean().default(false),
});

// Store configuration validation schema
export const storeConfigurationSchema = z.object({
  // Basic Store Information
  store_name: z
    .string()
    .min(2, 'Store name must be at least 2 characters')
    .max(100, 'Store name must be less than 100 characters')
    .regex(/^[a-zA-Z0-9\s\-_&.]+$/, 'Store name contains invalid characters'),

  store_handle: optText(
    z
      .string()
      .min(2, 'Store handle must be at least 2 characters')
      .max(50, 'Store handle must be less than 50 characters')
      .regex(
        VALIDATION_PATTERNS.storeHandle,
        'Store handle can only contain lowercase letters, numbers, and hyphens'
      )
  ),

  store_description: optText(
    z
      .string()
      .min(10, 'Store description must be at least 10 characters')
      .max(500, 'Store description must be less than 500 characters')
  ),

  store_email: optText(
    z
      .string()
      .email('Please enter a valid store email address')
      .max(100, 'Email must be less than 100 characters')
  ),

  gst_number: optText(
    z
      .string()
      .length(15, 'GST number must be exactly 15 characters')
      .regex(
        VALIDATION_PATTERNS.gst,
        'Please enter a valid GST number (e.g., 12ABCDE1234F1Z5)'
      )
  ),

  business_type: z.enum(BUSINESS_TYPES, {
    errorMap: () => ({ message: 'Please select a valid business type' }),
  }),

  business_category: z.enum(BUSINESS_CATEGORIES, {
    errorMap: () => ({ message: 'Please select a valid business category' }),
  }),

  // Contact Information
  email: z
    .string()
    .email('Please enter a valid email address')
    .max(100, 'Email must be less than 100 characters'),

  phone: optText(
    z
      .string()
      .min(10, 'Phone number must be at least 10 digits')
      .max(15, 'Phone number must be less than 15 digits')
      .regex(VALIDATION_PATTERNS.phone, 'Please enter a valid phone number')
  ),

  website: z.preprocess(
    emptyToUndefined,
    z
      .string()
      .regex(
        VALIDATION_PATTERNS.url,
        'Please enter a valid website URL (e.g., https://example.com)'
      )
      .optional()
  ),

  // Address Information
  address_line_1: optText(
    z
      .string()
      .min(5, 'Address line 1 must be at least 5 characters')
      .max(200, 'Address line 1 must be less than 200 characters')
  ),

  address_line_2: optText(
    z.string().max(200, 'Address line 2 must be less than 200 characters')
  ),

  city: optText(
    z
      .string()
      .min(2, 'City must be at least 2 characters')
      .max(50, 'City must be less than 50 characters')
      .regex(/^[a-zA-Z\s\-']+$/, 'City name contains invalid characters')
  ),

  state: optText(
    z
      .string()
      .min(2, 'State must be at least 2 characters')
      .max(50, 'State must be less than 50 characters')
      .regex(/^[a-zA-Z\s\-']+$/, 'State name contains invalid characters')
  ),

  pincode: optText(
    z
      .string()
      .length(6, 'Pincode must be exactly 6 digits')
      .regex(
        VALIDATION_PATTERNS.pincode,
        'Please enter a valid pincode (e.g., 400001)'
      )
  ),

  country: z
    .string()
    .min(2, 'Country must be at least 2 characters')
    .max(50, 'Country must be less than 50 characters')
    .default('India'),

  // Branding
  // store_theme: z.preprocess(
  //   emptyToUndefined,
  //   z.enum(STORE_THEMES, {
  //     errorMap: () => ({ message: 'Please select a valid store theme' }),
  //   }).default('modern')
  // ),

  store_logo: z.preprocess(
    v => (v instanceof File ? v : undefined), // ignore "", null, etc.
    z
      .instanceof(File)
      .refine(
        file => file.size <= 5 * 1024 * 1024,
        'Logo file size must be less than 5MB'
      )
      .refine(
        file => ['image/jpeg', 'image/png', 'image/webp'].includes(file.type),
        'Logo must be a JPEG, PNG, or WebP image'
      )
      .optional()
  ),

  store_logo_url: z.string().optional(),

  store_color_palette: z
    .object({
      vibrant: z.string().nullable(),
      vibrantLight: z.string().nullable(),
      vibrantDark: z.string().nullable(),
      muted: z.string().nullable(),
      mutedLight: z.string().nullable(),
      mutedDark: z.string().nullable(),
      dominant: z.string().nullable(),
      population: z.number(),
      extractedAt: z.string(),
    })
    .optional(),

  // Payment Methods
  payment_methods: paymentMethodsSchema.optional(),

  // System fields
  user_id: z.string().optional(),
  onboarding_step: z.number().min(1).max(3).default(1),
  onboarding_completed: z.boolean().default(false),
  id: z.string().optional(),
});

// export const storeConfigurationSchema = z.object({
//   // Basic Store Information
//   store_name: z
//     .string()
//     .min(2, 'Store name must be at least 2 characters')
//     .max(100, 'Store name must be less than 100 characters')
//     .regex(/^[a-zA-Z0-9\s\-_&.]+$/, 'Store name contains invalid characters'),

//   store_handle: z
//     .string()
//     .min(2, 'Store handle must be at least 2 characters')
//     .max(50, 'Store handle must be less than 50 characters')
//     .regex(
//       VALIDATION_PATTERNS.storeHandle,
//       'Store handle can only contain lowercase letters, numbers, and hyphens'
//     )
//     .optional(),

//   store_description: z
//     .string()
//     .min(10, 'Store description must be at least 10 characters')
//     .max(500, 'Store description must be less than 500 characters')
//     .optional(),

//   store_email: z
//     .string()
//     .email('Please enter a valid store email address')
//     .max(100, 'Email must be less than 100 characters')
//     .optional(),

//   gst_number: z
//     .string()
//     .length(15, 'GST number must be exactly 15 characters')
//     .regex(
//       VALIDATION_PATTERNS.gst,
//       'Please enter a valid GST number (e.g., 12ABCDE1234F1Z5)'
//     )
//     .optional(),

//   business_type: z.enum(BUSINESS_TYPES, {
//     errorMap: () => ({ message: 'Please select a valid business type' }),
//   }),

//   business_category: z.enum(BUSINESS_CATEGORIES, {
//     errorMap: () => ({ message: 'Please select a valid business category' }),
//   }),

//   // Contact Information
//   email: z
//     .string()
//     .email('Please enter a valid email address')
//     .max(100, 'Email must be less than 100 characters'),

//   phone: z
//     .string()
//     .min(10, 'Phone number must be at least 10 digits')
//     .max(15, 'Phone number must be less than 15 digits')
//     .regex(VALIDATION_PATTERNS.phone, 'Please enter a valid phone number')
//     .optional(),

//   website: z
//     .string()
//     .regex(
//       VALIDATION_PATTERNS.url,
//       'Please enter a valid website URL (e.g., https://example.com)'
//     )
//     .optional()
//     .or(z.literal('')),

//   // Address Information
//   address_line_1: z
//     .string()
//     .min(5, 'Address line 1 must be at least 5 characters')
//     .max(200, 'Address line 1 must be less than 200 characters')
//     .optional(),

//   address_line_2: z
//     .string()
//     .max(200, 'Address line 2 must be less than 200 characters')
//     .optional()
//     .or(z.literal(''))
//     .optional(),

//   city: z
//     .string()
//     .min(2, 'City must be at least 2 characters')
//     .max(50, 'City must be less than 50 characters')
//     .regex(/^[a-zA-Z\s\-']+$/, 'City name contains invalid characters')
//     .optional(),

//   state: z
//     .string()
//     .min(2, 'State must be at least 2 characters')
//     .max(50, 'State must be less than 50 characters')
//     .regex(/^[a-zA-Z\s\-']+$/, 'State name contains invalid characters')
//     .optional(),

//   pincode: z
//     .string()
//     .length(6, 'Pincode must be exactly 6 digits')
//     .regex(
//       VALIDATION_PATTERNS.pincode,
//       'Please enter a valid pincode (e.g., 400001)'
//     )
//     .optional(),

//   country: z
//     .string()
//     .min(2, 'Country must be at least 2 characters')
//     .max(50, 'Country must be less than 50 characters')
//     .default('India'),

//   // Branding
//   store_theme: z
//     .enum(STORE_THEMES, {
//       errorMap: () => ({ message: 'Please select a valid store theme' }),
//     })
//     .default('modern'),

//   store_logo: z
//     .instanceof(File)
//     .refine(
//       file => file.size <= 5 * 1024 * 1024, // 5MB
//       'Logo file size must be less than 5MB'
//     )
//     .refine(
//       file => ['image/jpeg', 'image/png', 'image/webp'].includes(file.type),
//       'Logo must be a JPEG, PNG, or WebP image'
//     )
//     .optional(),

//   // Payment Methods
//   payment_methods: paymentMethodsSchema.optional(),

//   // System fields
//   user_id: z.string().optional(),
//   onboarding_step: z.number().min(1).max(3).default(1),
//   onboarding_completed: z.boolean().default(false),
//   id: z.string().optional(), // For updates
// });

// Type inference
export type StoreConfigurationFormData = z.infer<
  typeof storeConfigurationSchema
>;
export type PaymentMethodsData = z.infer<typeof paymentMethodsSchema>;

// Validation state interface
export interface ValidationState {
  errors: Record<string, string>;
  isValid: boolean;
  isValidating: boolean;
  touchedFields: Set<string>;
}

// Field validation result
export interface FieldValidationResult {
  isValid: boolean;
  error?: string;
}

// Validation helper functions
export const validateField = (
  fieldName: keyof StoreConfigurationFormData,
  value: any,
  schema: z.ZodSchema = storeConfigurationSchema
): FieldValidationResult => {
  try {
    const fieldSchema = schema.shape[fieldName];
    if (fieldSchema) {
      fieldSchema.parse(value);
      return { isValid: true };
    }
    return { isValid: true };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        error: error.errors[0]?.message || 'Invalid value',
      };
    }
    return { isValid: false, error: 'Validation error' };
  }
};

// Validate entire form
export const validateStoreConfiguration = (
  data: Partial<StoreConfigurationFormData>
): { isValid: boolean; errors: Record<string, string> } => {
  try {
    storeConfigurationSchema.parse(data);
    return { isValid: true, errors: {} };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors: Record<string, string> = {};
      error.errors.forEach(err => {
        if (err.path.length > 0) {
          errors[err.path[0] as string] = err.message;
        }
      });
      return { isValid: false, errors };
    }
    return { isValid: false, errors: { general: 'Validation failed' } };
  }
};

// Auto-fill data extraction from auth storage
export const extractAutoFillData = (
  authUser: any,
  authStorage: any
): Partial<StoreConfigurationFormData> => {
  const autoFillData: Partial<StoreConfigurationFormData> = {};

  // From auth user
  if (authUser) {
    if (authUser.email) autoFillData.email = authUser.email;
    if (authUser.store_name) autoFillData.store_name = authUser.store_name;
    if (authUser.store_handle)
      autoFillData.store_handle = authUser.store_handle;
    if (authUser.first_name && authUser.last_name) {
      autoFillData.store_name =
        autoFillData.store_name || `${authUser.first_name}'s Store`;
    }
    if (authUser.contact_number) autoFillData.phone = authUser.contact_number;
  }

  // From auth storage (localStorage)
  if (authStorage?.user) {
    const user = authStorage.user;
    if (user.email) autoFillData.email = user.email;
    if (user.store_name) autoFillData.store_name = user.store_name;
    if (user.store_handle) autoFillData.store_handle = user.store_handle;
    if (user.contact_number) autoFillData.phone = user.contact_number;

    // Extract phone from metadata if available
    if (user.metadata?.phone) autoFillData.phone = user.metadata.phone;
    if (user.metadata?.store_name)
      autoFillData.store_name = user.metadata.store_name;
    if (user.metadata?.store_handle)
      autoFillData.store_handle = user.metadata.store_handle;
  }

  return autoFillData;
};
