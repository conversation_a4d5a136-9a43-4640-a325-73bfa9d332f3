import { create } from 'zustand';
import {
  storeConfigurationSchema,
  validateStoreConfiguration,
  validateField,
  extractAutoFillData,
  type StoreConfigurationFormData,
  type ValidationState,
  type FieldValidationResult,
  type PaymentMethodsData,
  BUSINESS_TYPES,
  BUSINESS_CATEGORIES,
} from '@/lib/validation/onboarding-schemas';
import { useAuthStore } from './authStore';

export interface StoreConfigurationData {
  // Basic Store Info
  store_name: string;
  store_description: string;
  store_email: string;
  gst_number: string;
  business_type: (typeof BUSINESS_TYPES)[number];
  business_category: (typeof BUSINESS_CATEGORIES)[number];
  store_handle: string;

  // Contact Info
  email: string;
  phone: string;
  website?: string;

  // Address
  address_line_1: string;
  address_line_2?: string;
  city: string;
  state: string;
  pincode: string;
  country: string;

  // Branding
  store_logo?: File;
  store_logo_url?: string;
  store_color_palette?: {
    vibrant: string | null;
    vibrantLight: string | null;
    vibrantDark: string | null;
    muted: string | null;
    mutedLight: string | null;
    mutedDark: string | null;
    dominant: string | null;
    population: number;
    extractedAt: string;
  };

  // Payment Methods
  payment_methods?: PaymentMethodsData;

  // System fields
  user_id: string;
  onboarding_step: number;
  onboarding_completed: boolean;
  id?: string; // For updates
}

export interface ProductData {
  name: string;
  price: string;
  description: string;
  category: string;
  sku?: string;
  inventory_quantity: string;
}

export interface OnboardingState {
  // Current step
  currentStep: number;

  // Data
  storeData: StoreConfigurationData;
  productData: ProductData;
  bulkUploadFile: File | null;

  // Status
  isLoading: boolean;
  error: string | null;

  // Progress tracking
  completedSteps: number[];

  // Change detection for smart navigation
  stepDataCache: {
    [stepIndex: number]: any;
  };
  hasUnsavedChanges: boolean;

  // Validation state
  validationState: ValidationState;

  // Auto-fill state
  isAutoFilled: boolean;
  autoFillSource: 'localStorage' | 'authStore' | 'backend' | null;

  // Actions
  setCurrentStep: (step: number) => void;
  updateStoreData: (data: Partial<StoreConfigurationData>) => void;
  updateProductData: (data: Partial<ProductData>) => void;
  setBulkUploadFile: (file: File | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  markStepCompleted: (step: number) => void;
  resetOnboarding: () => void;

  // Validation methods
  validateField: (
    fieldName: keyof StoreConfigurationData,
    value: any
  ) => FieldValidationResult;
  validateForm: () => { isValid: boolean; errors: Record<string, string> };
  clearValidationErrors: () => void;
  setFieldTouched: (fieldName: string) => void;

  // Auto-fill methods
  autoFillFromAuthStorage: () => void;
  autoFillFromLocalStorage: () => void;
  autoFillFromBackend: (backendData: any) => void;

  // Change detection methods
  cacheStepData: (stepIndex: number, data: any) => void;
  hasStepDataChanged: (stepIndex: number, currentData: any) => boolean;
  setHasUnsavedChanges: (hasChanges: boolean) => void;
  clearStepCache: (stepIndex: number) => void;
  isCreateOperation: () => boolean;
  isUpdateOperation: () => boolean;

  // API actions
  saveStoreConfiguration: () => Promise<void>;
  saveProduct: () => Promise<void>;
  handleBulkUpload: () => Promise<void>;
  completeOnboarding: () => Promise<void>;

  // Backend integration methods
  initializeFromBackend: (user: any) => Promise<void>;
  updateBackendOnboardingStatus: (stepData: any) => Promise<void>;
}

const initialStoreData: StoreConfigurationData = {
  store_name: '',
  store_handle: '',
  store_description: '',
  store_email: '',
  gst_number: '',
  business_type: 'individual',
  business_category: 'fashion',
  email: '',
  phone: '',
  website: '',
  address_line_1: '',
  address_line_2: '',
  city: '',
  state: '',
  pincode: '',
  country: 'India',
  store_logo: undefined,
  payment_methods: {
    cash_on_delivery: true,
    upi: true,
    credit_card: true,
    debit_card: true,
    net_banking: false,
    wallet: false,
    bnpl: false,
  },
  user_id: '',
  onboarding_step: 1,
  onboarding_completed: false,
  store_logo_url: '',
};

const initialProductData: ProductData = {
  name: '',
  price: '',
  description: '',
  category: '',
  sku: '',
  inventory_quantity: '1',
};

export const useOnboardingStore = create<OnboardingState>()((set, get) => ({
  // Initial state - No persistence, always start fresh
  currentStep: 0,
  storeData: initialStoreData,
  productData: initialProductData,
  bulkUploadFile: null,
  isLoading: false,
  error: null,
  completedSteps: [],
  stepDataCache: {},
  hasUnsavedChanges: false,

  // Validation state
  validationState: {
    errors: {},
    isValid: true,
    isValidating: false,
    touchedFields: new Set(),
  },

  // Auto-fill state
  isAutoFilled: false,
  autoFillSource: null,

  // Actions
  setCurrentStep: (step: number) => set({ currentStep: step }),

  updateStoreData: (data: Partial<StoreConfigurationData>) =>
    set(state => ({
      storeData: { ...state.storeData, ...data },
    })),

  updateProductData: (data: Partial<ProductData>) =>
    set(state => ({
      productData: { ...state.productData, ...data },
    })),

  setBulkUploadFile: (file: File | null) => set({ bulkUploadFile: file }),

  setLoading: (loading: boolean) => set({ isLoading: loading }),

  setError: (error: string | null) => set({ error }),

  markStepCompleted: (step: number) =>
    set(state => ({
      completedSteps: [...new Set([...state.completedSteps, step])],
    })),

  resetOnboarding: () =>
    set({
      currentStep: 0,
      storeData: initialStoreData,
      productData: initialProductData,
      bulkUploadFile: null,
      isLoading: false,
      error: null,
      completedSteps: [],
      stepDataCache: {},
      hasUnsavedChanges: false,
      validationState: {
        errors: {},
        isValid: true,
        isValidating: false,
        touchedFields: new Set(),
      },
      isAutoFilled: false,
      autoFillSource: null,
    }),

  // Validation methods
  validateField: (fieldName: keyof StoreConfigurationData, value: any) => {
    const result = validateField(fieldName, value, storeConfigurationSchema);

    set(state => ({
      validationState: {
        ...state.validationState,
        errors: {
          ...state.validationState.errors,
          [fieldName]: result.error || '',
        },
        touchedFields: new Set([
          ...state.validationState.touchedFields,
          fieldName,
        ]),
      },
    }));

    return result;
  },

  validateForm: () => {
    const { storeData } = get();
    const result = validateStoreConfiguration(storeData);

    set(state => ({
      validationState: {
        ...state.validationState,
        errors: result.errors,
        isValid: result.isValid,
      },
    }));

    return result;
  },

  clearValidationErrors: () =>
    set(state => ({
      validationState: {
        ...state.validationState,
        errors: {},
        isValid: true,
      },
    })),

  setFieldTouched: (fieldName: string) =>
    set(state => ({
      validationState: {
        ...state.validationState,
        touchedFields: new Set([
          ...state.validationState.touchedFields,
          fieldName,
        ]),
      },
    })),

  // Auto-fill methods
  autoFillFromAuthStorage: () => {
    try {
      const authStore = useAuthStore.getState();
      const authUser = authStore.user;

      if (authUser) {
        const autoFillData = extractAutoFillData(authUser, null);

        set(state => ({
          storeData: { ...state.storeData, ...autoFillData },
          isAutoFilled: true,
          autoFillSource: 'authStore',
        }));

        console.log('✅ Auto-filled from auth store:', autoFillData);
      }
    } catch (error) {
      console.error('❌ Failed to auto-fill from auth storage:', error);
    }
  },

  autoFillFromLocalStorage: () => {
    try {
      if (typeof window !== 'undefined') {
        const authStorage = localStorage.getItem('auth-storage');
        if (authStorage) {
          const parsedStorage = JSON.parse(authStorage);
          const autoFillData = extractAutoFillData(null, parsedStorage);

          set(state => ({
            storeData: { ...state.storeData, ...autoFillData },
            isAutoFilled: true,
            autoFillSource: 'localStorage',
          }));

          console.log('✅ Auto-filled from localStorage:', autoFillData);
        }
      }
    } catch (error) {
      console.error('❌ Failed to auto-fill from localStorage:', error);
    }
  },

  autoFillFromBackend: (backendData: any) => {
    try {
      if (backendData) {
        set(state => ({
          storeData: { ...state.storeData, ...backendData },
          isAutoFilled: true,
          autoFillSource: 'backend',
        }));

        console.log('✅ Auto-filled from backend:', backendData);
      }
    } catch (error) {
      console.error('❌ Failed to auto-fill from backend:', error);
    }
  },

  // Change detection methods
  cacheStepData: (stepIndex: number, data: any) =>
    set(state => ({
      stepDataCache: {
        ...state.stepDataCache,
        [stepIndex]: JSON.parse(JSON.stringify(data)), // Deep clone
      },
    })),

  hasStepDataChanged: (stepIndex: number, currentData: any) => {
    const { stepDataCache } = get();
    const cachedData = stepDataCache[stepIndex];
    if (!cachedData) return true; // No cached data means it's changed
    return JSON.stringify(cachedData) !== JSON.stringify(currentData);
  },

  setHasUnsavedChanges: (hasChanges: boolean) =>
    set({ hasUnsavedChanges: hasChanges }),

  clearStepCache: (stepIndex: number) =>
    set(state => {
      const newCache = { ...state.stepDataCache };
      delete newCache[stepIndex];
      return { stepDataCache: newCache };
    }),

  isCreateOperation: () => {
    const { storeData } = get();
    return !storeData.id; // No ID means create operation
  },

  isUpdateOperation: () => {
    const { storeData } = get();
    return !!storeData.id; // Has ID means update operation
  },

  // API actions
  saveStoreConfiguration: async () => {
    const { storeData, setLoading, setError, markStepCompleted } = get();

    try {
      setLoading(true);
      setError(null);

      const formData = new FormData();

      // Generate store handle
      const storeHandle = storeData.store_name
        .toLowerCase()
        .replace(/[^a-z0-9]/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '');

      const storeConfigData = {
        ...storeData,
        store_handle: storeHandle,
        onboarding_step: 1,
      };

      // Remove file from data object for JSON
      const { store_logo, ...jsonData } = storeConfigData;
      formData.append('data', JSON.stringify(jsonData));

      // Add logo file if present
      if (store_logo) {
        formData.append('files.store_logo', store_logo);
      }

      const response = await fetch('/api/strapi/store-configurations', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Failed to save store configuration');
      }

      const result = await response.json();
      console.log('✅ Store configuration saved:', result);
      markStepCompleted(0);
    } catch (error) {
      console.error('❌ Failed to save store configuration:', error);
      setError(
        error instanceof Error
          ? error.message
          : 'Failed to save store configuration'
      );
      throw error;
    } finally {
      setLoading(false);
    }
  },

  saveProduct: async () => {
    const { productData, setLoading, setError, markStepCompleted } = get();

    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/admin/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: productData.name,
          description: productData.description,
          handle: productData.name
            .toLowerCase()
            .replace(/[^a-z0-9]/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, ''),
          status: 'draft',
          variants: [
            {
              title: 'Default Variant',
              prices: [
                {
                  amount: parseFloat(productData.price) * 100, // Convert to cents
                  currency_code: 'inr',
                },
              ],
              inventory_quantity: parseInt(productData.inventory_quantity),
              sku: productData.sku || undefined,
            },
          ],
          categories: productData.category
            ? [{ name: productData.category }]
            : [],
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save product');
      }

      const result = await response.json();
      console.log('✅ Product saved:', result);
      markStepCompleted(1);
    } catch (error) {
      console.error('❌ Failed to save product:', error);
      setError(
        error instanceof Error ? error.message : 'Failed to save product'
      );
      throw error;
    } finally {
      setLoading(false);
    }
  },

  handleBulkUpload: async () => {
    const { bulkUploadFile, setLoading, setError, markStepCompleted } = get();

    if (!bulkUploadFile) {
      markStepCompleted(2); // Mark as completed even if skipped
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const formData = new FormData();
      formData.append('file', bulkUploadFile);

      const response = await fetch('/api/admin/products/bulk-upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Failed to upload products');
      }

      const result = await response.json();
      console.log('✅ Bulk upload completed:', result);
      markStepCompleted(2);
    } catch (error) {
      console.error('❌ Bulk upload failed:', error);
      setError(
        error instanceof Error ? error.message : 'Failed to upload products'
      );
      throw error;
    } finally {
      setLoading(false);
    }
  },

  completeOnboarding: async () => {
    const { storeData, setLoading, setError } = get();

    try {
      setLoading(true);
      setError(null);

      // Mark onboarding as completed
      await fetch('/api/strapi/store-configurations', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: storeData.user_id,
          onboarding_completed: true,
          onboarding_step: 3,
        }),
      });

      console.log('✅ Onboarding completed successfully');
    } catch (error) {
      console.error('❌ Failed to complete onboarding:', error);
      setError(
        error instanceof Error ? error.message : 'Failed to complete onboarding'
      );
      throw error;
    } finally {
      setLoading(false);
    }
  },

  // Backend integration methods
  initializeFromBackend: async (user: any) => {
    const {
      setLoading,
      setError,
      autoFillFromAuthStorage,
      autoFillFromLocalStorage,
    } = get();

    try {
      setLoading(true);

      // Determine current step based on user's onboarding status
      let currentStep = 0;
      const completedSteps: number[] = [];

      if (user.onboarding_store_config) {
        completedSteps.push(0);
        currentStep = 1;
      }
      if (user.onboarding_add_product) {
        completedSteps.push(1);
        currentStep = 2;
      }
      if (user.onboarding_bulk_upload) {
        completedSteps.push(2);
        currentStep = 2; // Stay on last step if all completed
      }

      // If onboarding is completed, set to final step
      if (user.onboarding_status === 'completed') {
        completedSteps.push(0, 1, 2);
        currentStep = 2;
      }

      // Fetch saved data from backend (in demo mode, use placeholder data)
      let savedStoreData = initialStoreData;
      let savedProductData = initialProductData;

      // In production, this would fetch from Strapi:
      // const storeConfigResponse = await fetch('/api/strapi/store-configurations?filters[user_email][$eq]=' + user.email);
      // const productResponse = await fetch('/api/strapi/products?filters[user_email][$eq]=' + user.email);

      // For demo, populate with sample data if steps are completed
      if (user.onboarding_store_config) {
        savedStoreData = {
          ...initialStoreData,
          store_name: user.metadata?.store_name || 'Test Store',
          store_description: 'A test store for onboarding',
          gst_number: '12ABCDE1234F1Z5',
          email: user.email,
          phone: user.metadata?.phone || '9876543210',
          address_line_1: '123 Test Street',
          city: 'Mumbai',
          state: 'Maharashtra',
          pincode: '400001',
          user_id: user.id,
          store_handle: user.store_handle || user.metadata?.store_handle,
          id: 'existing-store-config-id', // Mark as existing for update operations
        };
      } else {
        // Auto-fill for new users
        console.log('🔄 Auto-filling store configuration for new user...');

        // Try auth store first, then localStorage
        autoFillFromAuthStorage();
        if (!get().isAutoFilled) {
          autoFillFromLocalStorage();
        }

        // If still not auto-filled, extract from user object
        if (!get().isAutoFilled && user) {
          const autoFillData = extractAutoFillData(user, null);
          if (Object.keys(autoFillData).length > 0) {
            set(state => ({
              storeData: { ...state.storeData, ...autoFillData },
              isAutoFilled: true,
              autoFillSource: 'backend',
            }));
            console.log('✅ Auto-filled from user object:', autoFillData);
          }
        }
      }

      if (user.onboarding_add_product) {
        savedProductData = {
          name: 'Premium Wireless Headphones',
          price: '2999',
          description:
            'High-quality wireless headphones with noise cancellation, 30-hour battery life, and premium sound quality. Perfect for music lovers and professionals.',
          category: 'electronics',
          sku: 'PWH-001',
          inventory_quantity: '1',
        };
      }

      // Update store state
      set({
        currentStep,
        completedSteps,
        storeData: user.onboarding_store_config
          ? savedStoreData
          : get().storeData, // Keep auto-filled data for new users
        productData: savedProductData,
        isLoading: false,
      });

      console.log('✅ Onboarding state initialized from backend:', {
        currentStep,
        completedSteps,
        onboardingStatus: user.onboarding_status,
        isAutoFilled: get().isAutoFilled,
        autoFillSource: get().autoFillSource,
      });
    } catch (error) {
      console.error('❌ Failed to initialize from backend:', error);
      const { setError, setLoading } = get();
      setError('Failed to load onboarding data');
      setLoading(false);
    }
  },

  updateBackendOnboardingStatus: async (stepData: any) => {
    try {
      // In production, this would call the AuthContext method:
      // await updateOnboardingStatus(stepData);

      console.log('📝 Backend onboarding status updated:', stepData);
    } catch (error) {
      console.error('❌ Failed to update backend onboarding status:', error);
      throw error;
    }
  },
}));
